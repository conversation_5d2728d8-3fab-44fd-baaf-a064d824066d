<?php
/**
 * Smart WebP Conversion Global AJAX Handlers
 *
 * Contains global AJAX handlers and initialization functions
 * that work with the Enhanced WebP class only.
 *
 * @package Redco_Optimizer
 * @subpackage Smart_WebP_Conversion
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Global stats AJAX handler (works regardless of module status)
function redco_webp_ajax_get_stats() {
    // Use unified validation system
    if (!Redco_Security_Manager::validate_ajax_request('manage_options', null, array('redco_webp_stats', 'redco_optimizer_nonce'))) {
        return;
    }

    // CRITICAL FIX: Always return real stats when on the module page
    // The module status check was causing zero stats to be returned
    // even when the module was enabled and the user was viewing the module page

    // Get WebP module instance
    global $redco_webp_instance;
    if (!$redco_webp_instance) {
        if (class_exists('Redco_Smart_WebP_Conversion')) {
            $redco_webp_instance = new Redco_Smart_WebP_Conversion();
        } else {
            wp_send_json_error('WebP conversion class not available');
            return;
        }
    }

    // Call the instance method
    wp_send_json($redco_webp_instance->get_stats());
}




// Global bulk conversion AJAX handler
function redco_webp_ajax_bulk_convert() {
    // Use unified validation system with module check
    if (!Redco_Security_Manager::validate_ajax_request('manage_options', 'smart-webp-conversion', array('redco_webp_bulk_convert'))) {
        return;
    }

    // CRITICAL FIX: Enhanced error handling for class loading
    try {
        // Get WebP module instance
        global $redco_webp_instance;
        if (!$redco_webp_instance) {
            if (!class_exists('Redco_Smart_WebP_Conversion')) {
                require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/class-smart-webp-conversion.php';
            }

            if (class_exists('Redco_Smart_WebP_Conversion')) {
                $redco_webp_instance = new Redco_Smart_WebP_Conversion();
            } else {
                wp_send_json_error(array(
                    'message' => 'WebP conversion class not available',
                    'error_code' => 'CLASS_NOT_AVAILABLE'
                ));
                return;
            }
        }

        // Call the WebP class method
        $redco_webp_instance->ajax_bulk_convert();

    } catch (Exception $e) {
        wp_send_json_error(array(
            'message' => 'Error initializing WebP conversion: ' . $e->getMessage(),
            'error_code' => 'INITIALIZATION_ERROR'
        ));
    } catch (Error $e) {
        wp_send_json_error(array(
            'message' => 'Fatal error in WebP conversion: ' . $e->getMessage(),
            'error_code' => 'FATAL_ERROR'
        ));
    }
}

// Initialize Smart WebP Conversion module
function redco_init_smart_webp_conversion() {
    global $redco_webp_instance;

    // CRITICAL FIX: Always initialize the WebP instance for Media Library display
    // Even if the module is disabled, we need the instance to serve existing WebP images
    if (!$redco_webp_instance) {
        // Load the WebP conversion class
        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/class-smart-webp-conversion.php';
        }

        // Initialize WebP class
        try {
            $redco_webp_instance = new Redco_Smart_WebP_Conversion();

        } catch (Exception $e) {
            // Log initialization errors
            error_log("WebP initialization exception: " . $e->getMessage());
        } catch (Error $e) {
            // Log fatal errors
            error_log("WebP initialization fatal error: " . $e->getMessage());
        }
    }

    // CRITICAL FIX: Enqueue WebP admin scripts and localize only if module is enabled
    if (redco_is_module_enabled('smart-webp-conversion') && is_admin()) {
        add_action('admin_enqueue_scripts', 'redco_webp_enqueue_admin_scripts');
    }
}

// Enqueue WebP admin scripts with proper localization
function redco_webp_enqueue_admin_scripts($hook) {
    // More flexible hook checking for plugin pages
    $plugin_pages = array(
        'redco-optimizer',
        'redco_optimizer',
        'toplevel_page_redco-optimizer',
        'redco-optimizer_page_redco-optimizer-modules'
    );

    $is_plugin_page = false;
    foreach ($plugin_pages as $page) {
        if (strpos($hook, $page) !== false) {
            $is_plugin_page = true;
            break;
        }
    }

    if (!$is_plugin_page) {
        return;
    }



    // Enqueue WebP admin JavaScript
    wp_enqueue_script(
        'redco-webp-admin',
        REDCO_OPTIMIZER_PLUGIN_URL . 'modules/smart-webp-conversion/assets/js/admin.js',
        array('jquery'),
        REDCO_OPTIMIZER_VERSION,
        true
    );

    // Localize script with proper nonces and AJAX URL
    wp_localize_script('redco-webp-admin', 'redcoWebP', array(
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonces' => array(
            'bulk_convert' => wp_create_nonce('redco_webp_bulk_convert'),
            'stats' => wp_create_nonce('redco_webp_stats')
        ),
        'strings' => array(
            'converting' => __('Converting...', 'redco-optimizer'),
            'completed' => __('Conversion completed!', 'redco-optimizer'),
            'error' => __('Conversion failed', 'redco-optimizer'),
            'noImages' => __('No images to convert', 'redco-optimizer')
        )
    ));


}

// Only register the init hook if it hasn't been registered already
if (!has_action('init', 'redco_init_smart_webp_conversion')) {
    add_action('init', 'redco_init_smart_webp_conversion', 10);
}



// AJAX handler for getting processable images
function redco_webp_ajax_get_processable_images() {
    // Use unified validation system
    if (!Redco_Security_Manager::validate_ajax_request('manage_options', 'smart-webp-conversion', array('redco_webp_bulk_convert'))) {
        return;
    }

    try {
        // Get WebP instance
        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            $class_file = REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/class-smart-webp-conversion.php';
            if (!file_exists($class_file)) {
                throw new Exception('WebP class file not found: ' . $class_file);
            }
            require_once $class_file;
        }

        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            throw new Exception('WebP class could not be loaded');
        }

        $webp_instance = new Redco_Smart_WebP_Conversion();

        // Check if method exists
        if (!method_exists($webp_instance, 'get_all_processable_images')) {
            throw new Exception('Method get_all_processable_images does not exist');
        }

        $processable_images = $webp_instance->get_all_processable_images();

        wp_send_json_success(array(
            'total_images' => count($processable_images),
            'image_ids' => array_column($processable_images, 'ID'),
            'message' => 'Found ' . count($processable_images) . ' processable images'
        ));

    } catch (Exception $e) {
        wp_send_json_error('Failed to get processable images: ' . $e->getMessage());
    } catch (Error $e) {
        wp_send_json_error('Fatal error getting processable images: ' . $e->getMessage());
    }
}

// AJAX handler for getting recent conversions
function redco_webp_ajax_get_recent_conversions() {
    // Use unified validation system
    if (!Redco_Security_Manager::validate_ajax_request('manage_options', 'smart-webp-conversion', array('redco_webp_stats', 'redco_optimizer_nonce'))) {
        return;
    }

    try {
        $limit = isset($_POST['limit']) ? intval($_POST['limit']) : 10;
        $offset = isset($_POST['offset']) ? intval($_POST['offset']) : 0;

        // Get WebP instance
        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/class-smart-webp-conversion.php';
        }

        $webp_instance = new Redco_Smart_WebP_Conversion();
        $conversions = $webp_instance->get_recent_conversions($limit, $offset);
        $total_count = $webp_instance->get_conversions_count();

        wp_send_json_success(array(
            'conversions' => $conversions,
            'total_count' => $total_count,
            'has_more' => ($offset + $limit) < $total_count,
            'current_offset' => $offset,
            'current_limit' => $limit
        ));

    } catch (Exception $e) {
        wp_send_json_error('Failed to get recent conversions: ' . $e->getMessage());
    }
}

// AJAX handler for refreshing stats with real-time database queries
function redco_webp_ajax_refresh_stats() {
    // Security check
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    // Check if module is enabled
    if (!redco_is_module_enabled('smart-webp-conversion')) {
        wp_send_json_error('Module not enabled');
        return;
    }

    try {
        global $wpdb;

        // Real-time database queries for accurate stats

        // 1. Get all original format images that exist in the database
        $all_original_images = $wpdb->get_var("
            SELECT COUNT(*)
            FROM {$wpdb->posts}
            WHERE post_type = 'attachment'
            AND post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
        ");

        // 2. Get converted WebP images using serialized data format
        $converted_webp_images = $wpdb->get_results("
            SELECT pm.post_id, pm.meta_value
            FROM {$wpdb->postmeta} pm
            WHERE pm.meta_key = '_webp_conversion_data'
            AND (pm.meta_value LIKE '%s:9:\"converted\";b:1%' OR pm.meta_value LIKE '%\"converted\":true%')
        ");

        // 3. Count WebP images in posts table
        $webp_images_count = $wpdb->get_var("
            SELECT COUNT(*)
            FROM {$wpdb->posts}
            WHERE post_type = 'attachment'
            AND post_mime_type = 'image/webp'
        ");

        $converted_images = 0;
        $total_original_size = 0;
        $total_webp_size = 0;
        $total_savings = 0;
        $savings_percentages = array();

        // Process WebP conversion data to calculate savings
        foreach ($converted_webp_images as $row) {
            $conversion_data = maybe_unserialize($row->meta_value);
            if (is_array($conversion_data) && isset($conversion_data['converted']) && $conversion_data['converted']) {
                $converted_images++;

                if (isset($conversion_data['original_size']) && isset($conversion_data['webp_size'])) {
                    $original_size = intval($conversion_data['original_size']);
                    $webp_size = intval($conversion_data['webp_size']);

                    $total_original_size += $original_size;
                    $total_webp_size += $webp_size;

                    $savings = $original_size - $webp_size;
                    $total_savings += $savings;

                    // Calculate individual savings percentage
                    if ($original_size > 0) {
                        $savings_percentage = round(($savings / $original_size) * 100, 1);
                        $savings_percentages[] = $savings_percentage;
                    }
                }
            }
        }

        // FIXED: Calculate remaining unconverted images correctly
        // Get images that haven't been converted yet (exclude those with conversion meta)
        // CRITICAL FIX: Use both serialized and JSON patterns to match converted query
        $unconverted_images = $wpdb->get_var("
            SELECT COUNT(DISTINCT p.ID)
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_webp_conversion_data'
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
            AND (pm.meta_value IS NULL OR (pm.meta_value NOT LIKE '%s:9:\"converted\";b:1%' AND pm.meta_value NOT LIKE '%\"converted\":true%'))
        ");



        // For display purposes, total_images should represent remaining images
        $total_images = $unconverted_images;

        // Calculate conversion rate: converted / (total images that could be converted) * 100
        // Total convertible images = remaining + already converted
        $total_convertible_images = $unconverted_images + $converted_images;

        if ($total_convertible_images > 0) {
            $conversion_percentage = round(($converted_images / $total_convertible_images) * 100, 1);
        } else {
            $conversion_percentage = 0;
        }
        $avg_savings_percentage = count($savings_percentages) > 0 ? round(array_sum($savings_percentages) / count($savings_percentages), 1) : 0;

        // 3. Get convertible images count for button state (exclude already WebP images)
        // CRITICAL FIX: Use both serialized and JSON patterns to match converted query
        $convertible_count = $wpdb->get_var("
            SELECT COUNT(DISTINCT p.ID)
            FROM {$wpdb->posts} p
            LEFT JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id AND pm.meta_key = '_webp_conversion_data'
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif')
            AND (pm.meta_value IS NULL OR (pm.meta_value NOT LIKE '%s:9:\"converted\";b:1%' AND pm.meta_value NOT LIKE '%\"converted\":true%'))
        ");

        // Get recent conversions with actual timestamps from when the meta was created
        $recent_conversions_raw = $wpdb->get_results("
            SELECT pm.post_id, pm.meta_value, pm.meta_id, p.post_modified
            FROM {$wpdb->postmeta} pm
            INNER JOIN {$wpdb->posts} p ON pm.post_id = p.ID
            WHERE pm.meta_key = '_webp_conversion_data'
            AND (pm.meta_value LIKE '%s:9:\"converted\";b:1%' OR pm.meta_value LIKE '%\"converted\":true%')
            ORDER BY pm.meta_id DESC
            LIMIT 10
        ");

        $recent_conversions = array();
        $conversions_with_timestamps = array();

        // First pass: collect all conversions with their timestamps
        foreach ($recent_conversions_raw as $row) {
            $conversion_data = maybe_unserialize($row->meta_value);
            if (is_array($conversion_data) && isset($conversion_data['converted']) && $conversion_data['converted']) {
                // Format for display to match what JavaScript expects
                $post_title = get_the_title($row->post_id) ?: 'Unknown Image';
                $original_size = isset($conversion_data['original_size']) ? $conversion_data['original_size'] : 0;
                $webp_size = isset($conversion_data['webp_size']) ? $conversion_data['webp_size'] : 0;
                $savings_percentage = $original_size > 0 ? round((($original_size - $webp_size) / $original_size) * 100, 2) : 0;

                // Use timestamp from conversion data if available, otherwise use post_modified
                $timestamp = null;
                if (isset($conversion_data['timestamp'])) {
                    $timestamp = $conversion_data['timestamp'];
                } elseif (isset($conversion_data['converted_at'])) {
                    $timestamp = $conversion_data['converted_at'];
                } else {
                    // Use the post's last modified date as the conversion timestamp
                    $timestamp = strtotime($row->post_modified);
                }

                $formatted_conversion = array(
                    'title' => $post_title,
                    'formatted_original_size' => size_format($original_size),
                    'formatted_webp_size' => size_format($webp_size),
                    'savings_percentage' => $savings_percentage,
                    'formatted_date' => human_time_diff($timestamp, current_time('timestamp')) . ' ago',
                    'timestamp' => $timestamp,
                    'meta_id' => $row->meta_id
                );

                $conversions_with_timestamps[] = $formatted_conversion;
            }
        }

        // Sort by timestamp (most recent first) and then by meta_id as fallback
        usort($conversions_with_timestamps, function($a, $b) {
            if ($a['timestamp'] == $b['timestamp']) {
                return $b['meta_id'] - $a['meta_id']; // Higher meta_id first
            }
            return $b['timestamp'] - $a['timestamp']; // More recent timestamp first
        });

        // Take only the first 10 (most recent) and remove the sorting fields
        foreach (array_slice($conversions_with_timestamps, 0, 10) as $conversion) {
            unset($conversion['timestamp'], $conversion['meta_id']);
            $recent_conversions[] = $conversion;
        }



        // Prepare comprehensive stats response
        $stats = array(
            'total_images' => intval($total_images),
            'converted_images' => intval($converted_images),
            'unconverted_images' => intval($unconverted_images),
            'convertible_count' => intval($convertible_count),
            'conversion_percentage' => floatval($conversion_percentage),
            'total_original_size' => intval($total_original_size),
            'total_webp_size' => intval($total_webp_size),
            'total_savings' => intval($total_savings),
            'savings_percentage' => floatval($avg_savings_percentage),
            'server_support' => function_exists('imagewebp') && (imagetypes() & IMG_WEBP),
            'browser_support' => false // Will be set by JavaScript
        );





        wp_send_json_success(array(
            'stats' => $stats,
            'recent_conversions' => $recent_conversions,
            'message' => 'Statistics refreshed successfully from database',
            'timestamp' => current_time('mysql')
        ));

    } catch (Exception $e) {
        wp_send_json_error('Failed to refresh stats: ' . $e->getMessage());
    } catch (Error $e) {
        wp_send_json_error('Fatal error refreshing stats: ' . $e->getMessage());
    }
}

// AJAX handler for scanning images
function redco_webp_ajax_scan_images() {
    // Security check
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    // Check if module is enabled
    if (!redco_is_module_enabled('smart-webp-conversion')) {
        wp_send_json_error('Module not enabled');
        return;
    }

    try {
        global $wpdb;

        // Get all image attachments (including WebP)
        $all_images = $wpdb->get_results("
            SELECT p.ID, p.post_title, p.post_mime_type
            FROM {$wpdb->posts} p
            WHERE p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp')
            ORDER BY p.ID DESC
        ");

        // Get converted images
        $converted_images = $wpdb->get_results("
            SELECT DISTINCT pm.post_id
            FROM {$wpdb->postmeta} pm
            INNER JOIN {$wpdb->posts} p ON pm.post_id = p.ID
            WHERE pm.meta_key = '_webp_conversion_data'
            AND pm.meta_value LIKE '%\"converted\":true%'
            AND p.post_type = 'attachment'
            AND p.post_mime_type IN ('image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp')
        ");

        $converted_ids = array_column($converted_images, 'post_id');
        $unconverted_count = 0;
        $total_size = 0;
        $scan_results = array();

        // Analyze each image
        foreach ($all_images as $image) {
            $file_path = redco_safe_get_attached_file($image->ID);
            $is_converted = in_array($image->ID, $converted_ids);

            if ($file_path && file_exists($file_path)) {
                $file_size = filesize($file_path);
                $total_size += $file_size;

                if (!$is_converted) {
                    $unconverted_count++;
                }

                $scan_results[] = array(
                    'id' => $image->ID,
                    'title' => $image->post_title,
                    'mime_type' => $image->post_mime_type,
                    'file_size' => $file_size,
                    'is_converted' => $is_converted,
                    'file_exists' => true
                );
            }
        }

        // Calculate potential savings
        $estimated_savings = $unconverted_count * 0.3; // 30% average savings
        $estimated_size_savings = $total_size * 0.3;

        wp_send_json_success(array(
            'total_images' => count($all_images),
            'converted_images' => count($converted_images),
            'unconverted_images' => $unconverted_count,
            'total_size' => $total_size,
            'estimated_savings_percent' => 30,
            'estimated_size_savings' => $estimated_size_savings,
            'scan_results' => array_slice($scan_results, 0, 20), // Limit to 20 for performance
            'message' => "Scan complete: {$unconverted_count} images ready for WebP conversion"
        ));

    } catch (Exception $e) {
        wp_send_json_error('Failed to scan images: ' . $e->getMessage());
    }
}

// AJAX handler for analyzing WebP potential
function redco_webp_ajax_analyze_webp_potential() {
    // Security check
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    try {
        // Get WebP instance
        if (!class_exists('Redco_Smart_WebP_Conversion')) {
            require_once REDCO_OPTIMIZER_PLUGIN_DIR . 'modules/smart-webp-conversion/class-smart-webp-conversion.php';
        }

        $webp_instance = new Redco_Smart_WebP_Conversion();
        $processable_images = $webp_instance->get_all_processable_images();

        // Analyze file sizes and potential savings
        $total_original_size = 0;
        $analysis_details = array();

        foreach (array_slice($processable_images, 0, 10) as $image) {
            $file_path = redco_safe_get_attached_file($image->ID);
            if ($file_path && file_exists($file_path)) {
                $file_size = filesize($file_path);
                $total_original_size += $file_size;

                $analysis_details[] = array(
                    'id' => $image->ID,
                    'title' => $image->post_title,
                    'mime_type' => $image->post_mime_type,
                    'file_size' => $file_size,
                    'estimated_webp_size' => $file_size * 0.7, // 30% savings
                    'estimated_savings' => $file_size * 0.3
                );
            }
        }

        $estimated_total_savings = $total_original_size * 0.3;

        wp_send_json_success(array(
            'processable_count' => count($processable_images),
            'total_original_size' => $total_original_size,
            'estimated_total_savings' => $estimated_total_savings,
            'savings_percentage' => 30,
            'analysis_details' => $analysis_details,
            'server_support' => function_exists('imagewebp') && (imagetypes() & IMG_WEBP),
            'message' => count($processable_images) . ' images analyzed for WebP potential'
        ));

    } catch (Exception $e) {
        wp_send_json_error('Failed to analyze WebP potential: ' . $e->getMessage());
    }
}



// AJAX handler for database inspection
function redco_webp_ajax_inspect_database() {
    // Security check
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Insufficient permissions');
        return;
    }

    try {
        global $wpdb;

        // Get all attachments
        $all_attachments = $wpdb->get_results("
            SELECT ID, post_title, post_mime_type, post_status
            FROM {$wpdb->posts}
            WHERE post_type = 'attachment'
            ORDER BY ID DESC
        ");

        // Get image attachments only
        $image_attachments = $wpdb->get_results("
            SELECT ID, post_title, post_mime_type, post_status
            FROM {$wpdb->posts}
            WHERE post_type = 'attachment'
            AND post_mime_type LIKE 'image/%'
            ORDER BY ID DESC
        ");

        // Get all unique MIME types
        $all_mime_types = $wpdb->get_results("
            SELECT DISTINCT post_mime_type, COUNT(*) as count
            FROM {$wpdb->posts}
            WHERE post_type = 'attachment'
            GROUP BY post_mime_type
            ORDER BY count DESC
        ");

        // Get all WebP conversion meta
        $webp_meta = $wpdb->get_results("
            SELECT post_id, meta_value
            FROM {$wpdb->postmeta}
            WHERE meta_key = '_webp_conversion_data'
        ");

        // Get converted WebP meta
        $converted_meta = $wpdb->get_results("
            SELECT post_id, meta_value
            FROM {$wpdb->postmeta}
            WHERE meta_key = '_webp_conversion_data'
            AND meta_value LIKE '%\"converted\":true%'
        ");

        wp_send_json_success(array(
            'total_attachments' => count($all_attachments),
            'image_attachments' => count($image_attachments),
            'webp_meta_records' => count($webp_meta),
            'converted_meta_records' => count($converted_meta),
            'sample_attachments' => array_slice($all_attachments, 0, 5),
            'sample_image_attachments' => array_slice($image_attachments, 0, 5),
            'sample_webp_meta' => array_slice($webp_meta, 0, 3),
            'sample_converted_meta' => array_slice($converted_meta, 0, 3),
            'all_mime_types' => $all_mime_types
        ));

    } catch (Exception $e) {
        wp_send_json_error('Database inspection failed: ' . $e->getMessage());
    }
}

// Register AJAX handlers
if (is_admin()) {
    add_action('wp_ajax_redco_webp_bulk_convert', 'redco_webp_ajax_bulk_convert');
    add_action('wp_ajax_redco_webp_get_stats', 'redco_webp_ajax_get_stats');

    add_action('wp_ajax_redco_webp_get_processable_images', 'redco_webp_ajax_get_processable_images');
    add_action('wp_ajax_redco_webp_get_recent_conversions', 'redco_webp_ajax_get_recent_conversions');

    // NEW AJAX handlers for new functionality
    add_action('wp_ajax_redco_webp_refresh_stats', 'redco_webp_ajax_refresh_stats');
    add_action('wp_ajax_redco_webp_scan_images', 'redco_webp_ajax_scan_images');
    add_action('wp_ajax_redco_webp_analyze_webp_potential', 'redco_webp_ajax_analyze_webp_potential');
    add_action('wp_ajax_redco_webp_inspect_database', 'redco_webp_ajax_inspect_database');
}



<?php
/**
 * Asset Optimization Module for Redco Optimizer
 *
 * Unified module combining CSS/JS minification with critical resource optimization.
 * Consolidates: CSS/JS Minifier + Critical Resource Optimizer
 * 
 * Features:
 * - CSS/JS Minification and Compression
 * - Critical CSS Extraction and Inlining
 * - Resource Loading Optimization (defer, async, preload)
 * - Font Loading Optimization
 * - Resource Hints (preconnect, dns-prefetch)
 * - Unified Caching System
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Asset_Optimization {

    /**
     * Module settings
     */
    private $settings = array();

    /**
     * Critical CSS cache directory
     */
    private $critical_css_dir;

    /**
     * Unified cache directory
     */
    private $cache_dir;

    /**
     * Constructor
     */
    public function __construct() {
        if (redco_is_module_enabled('asset-optimization')) {
            $this->init();
        }
    }

    /**
     * Initialize the module
     */
    private function init() {
        // Set up cache directories
        $upload_dir = wp_upload_dir();
        $this->cache_dir = $upload_dir['basedir'] . '/redco-optimizer-cache/';
        $this->critical_css_dir = $this->cache_dir . 'critical-css/';
        
        // Create cache directories
        if (!file_exists($this->cache_dir)) {
            wp_mkdir_p($this->cache_dir);
        }
        if (!file_exists($this->critical_css_dir)) {
            wp_mkdir_p($this->critical_css_dir);
        }

        // Load settings
        $this->load_settings();

        // Initialize hooks
        $this->init_hooks();
    }

    /**
     * Load unified module settings
     */
    private function load_settings() {
        $this->settings = redco_get_module_option('asset-optimization', 'settings', array(
            // CSS/JS Minification settings
            'minify_css' => true,
            'minify_js' => true,
            'minify_inline' => true,
            'exclude_css' => array(),
            'exclude_js' => array('jquery-core', 'jquery-migrate'),
            
            // Critical Resource Optimization settings
            'critical_css' => true,
            'defer_non_critical' => true,
            'optimize_js' => true,
            'optimize_fonts' => true,
            'resource_hints' => true,
            'preconnect_google_fonts' => true,
            'preconnect_analytics' => true,
            
            // Advanced optimization settings
            'combine_css' => false, // Disabled by default for safety
            'combine_js' => false,  // Disabled by default for safety
            'async_js' => true,
            'defer_js' => true,
            'preload_critical' => true,
            'remove_unused_css' => false, // Advanced feature
            
            // Performance settings
            'enable_gzip' => true,
            'cache_duration' => 86400, // 24 hours
            'enable_brotli' => false // Advanced compression
        ));
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Admin AJAX handlers (only for module-specific actions)
        if (is_admin()) {
            add_action('wp_ajax_redco_clear_asset_cache', array($this, 'ajax_clear_cache'));
            add_action('wp_ajax_redco_generate_critical_css', array($this, 'ajax_generate_critical_css'));
            add_action('wp_ajax_redco_get_asset_optimization_stats', array($this, 'ajax_get_stats'));
            // Note: redco_optimize_assets is handled by the global progress tracker
            return;
        }

        // Frontend optimization hooks
        $this->init_frontend_hooks();
    }

    /**
     * Initialize frontend optimization hooks
     */
    private function init_frontend_hooks() {
        // Critical CSS injection (highest priority)
        if ($this->settings['critical_css']) {
            add_action('wp_head', array($this, 'inject_critical_css'), 1);
        }

        // Resource hints (early in head)
        if ($this->settings['resource_hints']) {
            add_action('wp_head', array($this, 'add_resource_hints'), 2);
        }

        // Font optimization
        if ($this->settings['optimize_fonts']) {
            add_action('wp_head', array($this, 'optimize_font_loading'), 3);
        }

        // CSS optimization
        if ($this->settings['minify_css']) {
            add_filter('style_loader_src', array($this, 'optimize_css_file'), 10, 2);
        }

        // CSS deferring
        if ($this->settings['defer_non_critical']) {
            add_filter('style_loader_tag', array($this, 'defer_non_critical_css'), 10, 4);
        }

        // JavaScript optimization
        if ($this->settings['minify_js'] || $this->settings['optimize_js']) {
            add_filter('script_loader_src', array($this, 'optimize_js_file'), 10, 2);
            add_filter('script_loader_tag', array($this, 'optimize_javascript_loading'), 10, 3);
        }

        // Inline minification
        if ($this->settings['minify_inline']) {
            add_action('wp_head', array($this, 'start_output_buffering'), 0);
            add_action('wp_footer', array($this, 'end_output_buffering'), 999);
        }

        // Cache clearing hooks
        add_action('save_post', array($this, 'clear_critical_cache'));
        add_action('switch_theme', array($this, 'clear_all_cache'));
    }

    /**
     * Inject critical CSS into head
     */
    public function inject_critical_css() {
        $critical_css = $this->get_critical_css();

        if (!empty($critical_css)) {
            echo '<!-- Redco Asset Optimization Critical CSS -->' . "\n";
            echo '<style id="redco-critical-css">' . $critical_css . '</style>' . "\n";
            echo '<!-- End Redco Critical CSS -->' . "\n";

            // Add preload for full stylesheets
            $this->add_stylesheet_preload();
        }
    }

    /**
     * Add resource hints for performance
     */
    public function add_resource_hints() {
        echo '<!-- Redco Asset Optimization Resource Hints -->' . "\n";

        // Preconnect to common external domains
        $preconnect_domains = $this->get_preconnect_domains();
        foreach ($preconnect_domains as $domain) {
            echo '<link rel="preconnect" href="' . esc_url($domain) . '" crossorigin>' . "\n";
        }

        // DNS prefetch for external resources
        $dns_prefetch_domains = $this->get_dns_prefetch_domains();
        foreach ($dns_prefetch_domains as $domain) {
            echo '<link rel="dns-prefetch" href="' . esc_url($domain) . '">' . "\n";
        }

        echo '<!-- End Redco Resource Hints -->' . "\n";
    }

    /**
     * Optimize font loading with font-display: swap
     */
    public function optimize_font_loading() {
        echo '<style>@font-face{font-display:swap;}</style>' . "\n";
        
        // Preconnect to Google Fonts if enabled
        if ($this->settings['preconnect_google_fonts']) {
            echo '<link rel="preconnect" href="https://fonts.googleapis.com">' . "\n";
            echo '<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>' . "\n";
        }
    }

    /**
     * Optimize CSS file (minification + caching)
     */
    public function optimize_css_file($src, $handle) {
        // Skip if invalid or excluded
        if (!$this->should_optimize_file($src, $handle, 'css')) {
            return $src;
        }

        return $this->get_optimized_file_url($src, 'css', $handle);
    }

    /**
     * Optimize JavaScript file (minification + caching)
     */
    public function optimize_js_file($src, $handle) {
        // Skip if invalid or excluded
        if (!$this->should_optimize_file($src, $handle, 'js')) {
            return $src;
        }

        return $this->get_optimized_file_url($src, 'js', $handle);
    }

    /**
     * Check if file should be optimized
     */
    private function should_optimize_file($src, $handle, $type) {
        // Basic validation
        if (!$src || !is_string($src) || empty($src)) {
            return false;
        }

        // Check exclusions
        $exclude_setting = 'exclude_' . $type;
        if (in_array($handle, $this->settings[$exclude_setting])) {
            return false;
        }

        // Only process local files
        $home_url = home_url();
        if (!$home_url || strpos($src, $home_url) === false) {
            return false;
        }

        // Skip already minified files
        if (strpos($src, '.min.') !== false) {
            return false;
        }

        return true;
    }

    /**
     * Get optimized file URL with unified caching
     */
    private function get_optimized_file_url($src, $type, $handle) {
        // Remove query string for cache key
        $clean_src = strtok($src, '?');
        $cache_key = md5($clean_src . $type . $handle);

        // Check transient cache first
        $transient_key = 'redco_optimized_' . $cache_key;
        $cached_url = get_transient($transient_key);

        if ($cached_url !== false) {
            return $cached_url;
        }

        // Get optimized cache directory
        $optimized_dir = $this->cache_dir . 'optimized/';
        if (!file_exists($optimized_dir)) {
            wp_mkdir_p($optimized_dir);
        }

        $optimized_file = $optimized_dir . $cache_key . '.' . $type;
        $optimized_url = str_replace(ABSPATH, home_url('/'), $optimized_file);

        // Check if optimized file exists and is current
        if (file_exists($optimized_file)) {
            $source_file = $this->url_to_path($clean_src);
            if (file_exists($source_file) && filemtime($optimized_file) >= filemtime($source_file)) {
                set_transient($transient_key, $optimized_url, HOUR_IN_SECONDS);
                return $optimized_url;
            }
        }

        // Create optimized file
        $source_file = $this->url_to_path($clean_src);
        if (file_exists($source_file)) {
            $content = file_get_contents($source_file);
            $optimized_content = $this->optimize_content($content, $type);

            if ($optimized_content !== false) {
                file_put_contents($optimized_file, $optimized_content);
                
                // Update statistics
                $this->update_optimization_stats($src, strlen($content), strlen($optimized_content), $type);
                
                set_transient($transient_key, $optimized_url, HOUR_IN_SECONDS);
                return $optimized_url;
            }
        }

        return $src;
    }

    /**
     * Optimize content based on type
     */
    private function optimize_content($content, $type) {
        switch ($type) {
            case 'css':
                return $this->minify_css($content);
            case 'js':
                return $this->minify_js($content);
            default:
                return $content;
        }
    }

    /**
     * Minify CSS content
     */
    private function minify_css($css) {
        // Remove comments
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);

        // Remove unnecessary whitespace
        $css = str_replace(array("\r\n", "\r", "\n", "\t"), '', $css);
        $css = preg_replace('/\s+/', ' ', $css);

        // Remove whitespace around specific characters
        $css = str_replace(array(' {', '{ ', ' }', '} ', ' :', ': ', ' ;', '; ', ' ,', ', '),
                          array('{', '{', '}', '}', ':', ':', ';', ';', ',', ','), $css);

        // Remove trailing semicolon before closing brace
        $css = str_replace(';}', '}', $css);

        return trim($css);
    }

    /**
     * Minify JavaScript content (conservative approach)
     */
    private function minify_js($js) {
        // Skip minification for complex JavaScript to prevent errors
        if (strpos($js, 'eval(') !== false ||
            strpos($js, 'new Function') !== false ||
            strpos($js, 'document.write') !== false) {
            return $js;
        }

        // Remove single-line comments (preserve URLs)
        $js = preg_replace('/(?<!:)\/\/(?![\/\*]).*$/m', '', $js);

        // Remove multi-line comments (preserve license comments)
        $js = preg_replace('/\/\*(?!\!)[\s\S]*?\*\//', '', $js);

        // Remove unnecessary whitespace
        $js = preg_replace('/\s+/', ' ', $js);

        // Remove whitespace around operators
        $js = preg_replace('/\s*([{}();,])\s*/', '$1', $js);

        return trim($js);
    }

    /**
     * Defer non-critical CSS using media="print" technique
     */
    public function defer_non_critical_css($html, $handle, $href, $media) {
        // Skip critical stylesheets
        if ($this->is_critical_stylesheet($handle)) {
            return $html;
        }

        // Skip admin stylesheets
        if (strpos($handle, 'admin') !== false) {
            return $html;
        }

        // Defer non-critical CSS
        $deferred_html = str_replace("media='$media'", "media='print' onload=\"this.media='$media'\"", $html);
        $deferred_html = str_replace('media="' . $media . '"', 'media="print" onload="this.media=\'' . $media . '\'"', $deferred_html);

        // Add noscript fallback
        $deferred_html .= '<noscript>' . $html . '</noscript>';

        return $deferred_html;
    }

    /**
     * Optimize JavaScript loading with async/defer
     */
    public function optimize_javascript_loading($tag, $handle, $src) {
        // Skip critical scripts
        if ($this->is_critical_script($handle)) {
            return $tag;
        }

        // Skip admin scripts
        if (strpos($handle, 'admin') !== false) {
            return $tag;
        }

        // Add async or defer based on script type
        if ($this->should_defer_script($handle)) {
            $tag = str_replace(' src', ' defer src', $tag);
        } elseif ($this->settings['async_js']) {
            $tag = str_replace(' src', ' async src', $tag);
        }

        return $tag;
    }

    /**
     * Get critical CSS for current page
     */
    public function get_critical_css() {
        $page_id = $this->get_page_identifier();
        $cache_key = 'redco_critical_css_' . $page_id;

        // Try transient cache first
        $cached_css = get_transient($cache_key);
        if ($cached_css !== false) {
            return $cached_css;
        }

        // Try specific page critical CSS file
        $cache_file = $this->critical_css_dir . 'critical-' . $page_id . '.css';
        if (file_exists($cache_file)) {
            $css_content = file_get_contents($cache_file);
            set_transient($cache_key, $css_content, HOUR_IN_SECONDS);
            return $css_content;
        }

        // Try homepage critical CSS as fallback
        $homepage_cache_file = $this->critical_css_dir . 'critical-homepage.css';
        if (file_exists($homepage_cache_file)) {
            $css_content = file_get_contents($homepage_cache_file);
            set_transient($cache_key, $css_content, HOUR_IN_SECONDS);
            return $css_content;
        }

        return '';
    }

    /**
     * Get unique page identifier for caching
     */
    private function get_page_identifier() {
        if (is_front_page()) {
            return 'homepage';
        } elseif (is_single()) {
            return 'post-' . get_the_ID();
        } elseif (is_page()) {
            return 'page-' . get_the_ID();
        } elseif (is_category()) {
            return 'category-' . get_queried_object_id();
        } elseif (is_tag()) {
            return 'tag-' . get_queried_object_id();
        } else {
            return 'archive-' . md5($_SERVER['REQUEST_URI']);
        }
    }

    /**
     * Convert URL to file path
     */
    private function url_to_path($url) {
        if (!$url || !is_string($url) || empty($url)) {
            return '';
        }

        $url = strtok($url, '?'); // Remove query string
        $home_url = home_url('/');

        if (strpos($url, $home_url) === 0) {
            return str_replace($home_url, ABSPATH, $url);
        }

        return '';
    }

    /**
     * Check if stylesheet is critical and should not be deferred
     */
    private function is_critical_stylesheet($handle) {
        $critical_handles = array(
            'wp-block-library',
            'wp-block-library-theme',
            'global-styles',
            'admin-bar'
        );

        return in_array($handle, $critical_handles);
    }

    /**
     * Check if script is critical and should not be deferred
     */
    private function is_critical_script($handle) {
        $critical_handles = array(
            'jquery',
            'jquery-core',
            'jquery-migrate',
            'wp-polyfill',
            'admin-bar'
        );

        return in_array($handle, $critical_handles);
    }

    /**
     * Determine if script should be deferred vs async
     */
    private function should_defer_script($handle) {
        // Scripts that depend on DOM should be deferred
        $defer_handles = array(
            'wp-dom-ready',
            'wp-hooks',
            'wp-i18n',
            'contact-form-7'
        );

        return in_array($handle, $defer_handles);
    }

    /**
     * Get domains for preconnect hints
     */
    private function get_preconnect_domains() {
        $domains = array();

        // Google Fonts
        if ($this->settings['preconnect_google_fonts']) {
            $domains[] = 'https://fonts.googleapis.com';
            $domains[] = 'https://fonts.gstatic.com';
        }

        // Google Analytics
        if ($this->settings['preconnect_analytics']) {
            $domains[] = 'https://www.google-analytics.com';
            $domains[] = 'https://www.googletagmanager.com';
        }

        // Allow filtering
        return apply_filters('redco_asset_optimization_preconnect_domains', $domains);
    }

    /**
     * Get domains for DNS prefetch hints
     */
    private function get_dns_prefetch_domains() {
        $domains = array(
            '//ajax.googleapis.com',
            '//cdnjs.cloudflare.com',
            '//maxcdn.bootstrapcdn.com'
        );

        // Allow filtering
        return apply_filters('redco_asset_optimization_dns_prefetch_domains', $domains);
    }

    /**
     * Add stylesheet preload links
     */
    private function add_stylesheet_preload() {
        global $wp_styles;

        if (isset($wp_styles->queue)) {
            foreach ($wp_styles->queue as $handle) {
                if (isset($wp_styles->registered[$handle]) && !$this->is_critical_stylesheet($handle)) {
                    $src = $wp_styles->registered[$handle]->src;
                    echo '<link rel="preload" href="' . esc_url($src) . '" as="style" onload="this.onload=null;this.rel=\'stylesheet\'">' . "\n";
                }
            }
        }
    }

    /**
     * Start output buffering for inline minification
     */
    public function start_output_buffering() {
        ob_start(array($this, 'minify_inline_content'));
    }

    /**
     * End output buffering
     */
    public function end_output_buffering() {
        if (ob_get_level()) {
            ob_end_flush();
        }
    }

    /**
     * Minify inline content (CSS and JS)
     */
    public function minify_inline_content($content) {
        // Minify inline CSS
        $content = preg_replace_callback(
            '/<style[^>]*>(.*?)<\/style>/is',
            function($matches) {
                $minified = $this->minify_css($matches[1]);
                return '<style>' . $minified . '</style>';
            },
            $content
        );

        // Minify inline JavaScript
        $content = preg_replace_callback(
            '/<script[^>]*>(.*?)<\/script>/is',
            function($matches) {
                // Skip if script has src attribute
                if (strpos($matches[0], 'src=') !== false) {
                    return $matches[0];
                }

                $minified = $this->minify_js($matches[1]);
                return str_replace($matches[1], $minified, $matches[0]);
            },
            $content
        );

        return $content;
    }

    /**
     * Update optimization statistics
     */
    private function update_optimization_stats($src, $original_size, $optimized_size, $type) {
        $stats = get_option('redco_asset_optimization_stats', array(
            'css_files' => 0,
            'js_files' => 0,
            'total_files' => 0,
            'original_size' => 0,
            'optimized_size' => 0,
            'bytes_saved' => 0,
            'last_optimized' => 0,
            'compression_ratio' => 0
        ));

        // Update counters
        $stats[$type . '_files']++;
        $stats['total_files']++;
        $stats['original_size'] += $original_size;
        $stats['optimized_size'] += $optimized_size;
        $stats['bytes_saved'] = max(0, $stats['original_size'] - $stats['optimized_size']);
        $stats['last_optimized'] = time();

        // Calculate compression ratio
        if ($stats['original_size'] > 0) {
            $stats['compression_ratio'] = round((1 - ($stats['optimized_size'] / $stats['original_size'])) * 100, 1);
        }

        update_option('redco_asset_optimization_stats', $stats);
    }

    /**
     * Get comprehensive optimization statistics
     */
    public function get_stats() {
        $stats = get_option('redco_asset_optimization_stats', array(
            'css_files' => 0,
            'js_files' => 0,
            'total_files' => 0,
            'original_size' => 0,
            'optimized_size' => 0,
            'bytes_saved' => 0,
            'last_optimized' => 0,
            'compression_ratio' => 0
        ));

        // Get cache directory sizes
        $optimized_dir = $this->cache_dir . 'optimized/';
        $critical_dir = $this->critical_css_dir;

        $cache_size = 0;
        $cached_files = 0;

        if (is_dir($optimized_dir)) {
            $cache_size += $this->calculate_directory_size($optimized_dir);
            $cached_files += $this->count_files_in_directory($optimized_dir);
        }

        if (is_dir($critical_dir)) {
            $cache_size += $this->calculate_directory_size($critical_dir);
            $cached_files += $this->count_files_in_directory($critical_dir);
        }

        return array(
            'css_files' => $stats['css_files'],
            'js_files' => $stats['js_files'],
            'total_files' => max($stats['total_files'], $cached_files),
            'files_optimized' => $cached_files, // Backward compatibility
            'original_size' => $stats['original_size'],
            'original_size_formatted' => redco_format_bytes($stats['original_size']),
            'optimized_size' => $stats['optimized_size'],
            'optimized_size_formatted' => redco_format_bytes($stats['optimized_size']),
            'bytes_saved' => $stats['bytes_saved'],
            'bytes_saved_formatted' => redco_format_bytes($stats['bytes_saved']),
            'compression_ratio' => $stats['compression_ratio'],
            'cache_size' => redco_format_bytes($cache_size),
            'cache_size_bytes' => $cache_size,
            'cached_files' => $cached_files,
            'last_optimized' => $stats['last_optimized'],
            'enabled' => redco_is_module_enabled('asset-optimization'),
            'critical_css_enabled' => $this->settings['critical_css'],
            'minification_enabled' => $this->settings['minify_css'] || $this->settings['minify_js'],
            'performance_impact' => $this->calculate_performance_impact($stats)
        );
    }

    /**
     * Calculate directory size recursively
     */
    private function calculate_directory_size($directory) {
        $size = 0;

        if (!is_dir($directory)) {
            return 0;
        }

        try {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
            );

            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $size += $file->getSize();
                }
            }
        } catch (Exception $e) {
            // Handle directory access errors silently
            return 0;
        }

        return $size;
    }

    /**
     * Count files in directory
     */
    private function count_files_in_directory($directory) {
        $count = 0;

        if (!is_dir($directory)) {
            return 0;
        }

        try {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
            );

            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    $count++;
                }
            }
        } catch (Exception $e) {
            // Handle directory access errors silently
            return 0;
        }

        return $count;
    }

    /**
     * Calculate performance impact
     */
    private function calculate_performance_impact($stats) {
        if ($stats['total_files'] === 0) {
            return array(
                'status' => 'no_data',
                'message' => 'No assets optimized yet',
                'bandwidth_saved' => 0
            );
        }

        $bandwidth_saved = $stats['bytes_saved'];
        $compression_ratio = $stats['compression_ratio'];

        $status = 'excellent';
        $message = "Asset optimization is working excellently with {$compression_ratio}% size reduction";

        if ($compression_ratio < 10) {
            $status = 'poor';
            $message = "Asset optimization has minimal impact. Consider enabling more features.";
        } elseif ($compression_ratio < 20) {
            $status = 'fair';
            $message = "Asset optimization is working but could be improved.";
        } elseif ($compression_ratio < 30) {
            $status = 'good';
            $message = "Asset optimization is working well with {$compression_ratio}% size reduction";
        }

        return array(
            'status' => $status,
            'message' => $message,
            'bandwidth_saved' => $bandwidth_saved,
            'bandwidth_saved_formatted' => redco_format_bytes($bandwidth_saved),
            'estimated_requests_faster' => $stats['total_files'],
            'critical_css_active' => $this->settings['critical_css'],
            'resource_hints_active' => $this->settings['resource_hints']
        );
    }

    /**
     * AJAX handler: Clear all asset optimization cache
     */
    public function ajax_clear_cache() {
        // Flexible nonce verification - accept both specific and global nonces
        $nonce_verified = false;
        if (isset($_POST['nonce'])) {
            $nonce_verified = wp_verify_nonce($_POST['nonce'], 'redco_asset_optimization_nonce') ||
                             wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce');
        }

        if (!$nonce_verified) {
            wp_send_json_error(array('message' => 'Security check failed'));
            return;
        }

        // Clear all caches
        $cleared = $this->clear_all_cache();

        wp_send_json_success(array(
            'message' => $cleared ? 'Asset optimization cache cleared successfully!' : 'Cache was already empty.',
            'cleared_files' => $cleared
        ));
    }

    /**
     * AJAX handler: Generate critical CSS
     */
    public function ajax_generate_critical_css() {
        // Flexible nonce verification - accept both specific and global nonces
        $nonce_verified = false;
        if (isset($_POST['nonce'])) {
            $nonce_verified = wp_verify_nonce($_POST['nonce'], 'redco_asset_optimization_nonce') ||
                             wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce');
        }

        if (!$nonce_verified) {
            wp_send_json_error(array('message' => 'Security check failed'));
            return;
        }

        $url = isset($_POST['url']) ? esc_url_raw($_POST['url']) : home_url();

        // Generate critical CSS for the specified URL
        $critical_css = $this->generate_critical_css_for_url($url);

        if ($critical_css) {
            wp_send_json_success(array(
                'message' => 'Critical CSS generated successfully!',
                'css_length' => strlen($critical_css),
                'url' => $url
            ));
        } else {
            wp_send_json_error(array(
                'message' => 'Failed to generate critical CSS. Please try again.'
            ));
        }
    }

    /**
     * AJAX handler: Optimize assets
     */
    public function ajax_optimize_assets() {
        // Flexible nonce verification - accept both specific and global nonces
        $nonce_verified = false;
        if (isset($_POST['nonce'])) {
            $nonce_verified = wp_verify_nonce($_POST['nonce'], 'redco_asset_optimization_nonce') ||
                             wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce');
        }

        if (!$nonce_verified) {
            wp_send_json_error(array(
                'message' => 'Security check failed'
            ));
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => 'Insufficient permissions'
            ));
            return;
        }

        try {
            // Force optimization of all assets
            $result = $this->force_optimize_all_assets();

            if ($result) {
                wp_send_json_success(array(
                    'message' => 'Asset optimization completed successfully!',
                    'stats' => $this->get_stats()
                ));
            } else {
                wp_send_json_error(array(
                    'message' => 'Asset optimization failed. Please check error logs for details.'
                ));
            }
        } catch (Exception $e) {
            error_log('Asset Optimization AJAX Error: ' . $e->getMessage());
            wp_send_json_error(array(
                'message' => 'Asset optimization failed: ' . $e->getMessage()
            ));
        }
    }

    /**
     * AJAX handler: Get current optimization statistics
     */
    public function ajax_get_stats() {
        // Flexible nonce verification - accept both specific and global nonces
        $nonce_verified = false;
        if (isset($_POST['nonce'])) {
            $nonce_verified = wp_verify_nonce($_POST['nonce'], 'redco_asset_optimization_nonce') ||
                             wp_verify_nonce($_POST['nonce'], 'redco_optimizer_nonce');
        }

        if (!$nonce_verified) {
            wp_send_json_error(array('message' => 'Security check failed'));
            return;
        }

        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Insufficient permissions'));
            return;
        }

        try {
            $stats = $this->get_stats();
            wp_send_json_success($stats);
        } catch (Exception $e) {
            error_log('Asset Optimization Stats AJAX Error: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'Failed to get optimization statistics'));
        }
    }

    /**
     * Clear all optimization cache
     */
    public function clear_all_cache() {
        $cleared_files = 0;

        // Clear optimized files cache
        $optimized_dir = $this->cache_dir . 'optimized/';
        if (is_dir($optimized_dir)) {
            $cleared_files += $this->clear_directory($optimized_dir);
        }

        // Clear critical CSS cache
        if (is_dir($this->critical_css_dir)) {
            $cleared_files += $this->clear_directory($this->critical_css_dir);
        }

        // Clear transient caches
        $this->clear_transient_caches();

        // Get current stats to report how many items were cleared
        $stats = get_option('redco_asset_optimization_stats', array());
        $total_items_cleared = max($cleared_files, isset($stats['total_files']) ? $stats['total_files'] : 0);

        // Reset optimization statistics
        $this->reset_optimization_stats();

        return $total_items_cleared;
    }

    /**
     * Clear critical CSS cache only
     */
    public function clear_critical_cache() {
        if (is_dir($this->critical_css_dir)) {
            $this->clear_directory($this->critical_css_dir);
        }

        // Clear critical CSS transients
        global $wpdb;
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_redco_critical_css_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_redco_critical_css_%'");
    }

    /**
     * Clear directory recursively
     */
    private function clear_directory($directory) {
        $cleared_files = 0;

        if (!is_dir($directory)) {
            return 0;
        }

        try {
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS),
                RecursiveIteratorIterator::CHILD_FIRST
            );

            foreach ($iterator as $file) {
                if ($file->isFile()) {
                    unlink($file->getPathname());
                    $cleared_files++;
                } elseif ($file->isDir()) {
                    rmdir($file->getPathname());
                }
            }
        } catch (Exception $e) {
            // Handle directory access errors silently
        }

        return $cleared_files;
    }

    /**
     * Clear transient caches
     */
    private function clear_transient_caches() {
        global $wpdb;

        // Clear optimization transients
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_redco_optimized_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_redco_optimized_%'");

        // Clear critical CSS transients
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_redco_critical_css_%'");
        $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_redco_critical_css_%'");
    }

    /**
     * Reset optimization statistics
     */
    private function reset_optimization_stats() {
        $default_stats = array(
            'css_files' => 0,
            'js_files' => 0,
            'total_files' => 0,
            'original_size' => 0,
            'optimized_size' => 0,
            'bytes_saved' => 0,
            'last_optimized' => 0,
            'compression_ratio' => 0
        );

        update_option('redco_asset_optimization_stats', $default_stats);
    }

    /**
     * Generate critical CSS for a specific URL
     */
    public function generate_critical_css_for_url($url) {
        // This is a simplified implementation
        // In production, this would use a headless browser or CSS analysis tool

        $page_id = $this->get_page_identifier_from_url($url);
        $cache_file = $this->critical_css_dir . 'critical-' . $page_id . '.css';

        // For now, generate basic critical CSS
        $critical_css = $this->generate_basic_critical_css();

        if ($critical_css) {
            file_put_contents($cache_file, $critical_css);
            return $critical_css;
        }

        return false;
    }

    /**
     * Generate basic critical CSS (placeholder implementation)
     */
    private function generate_basic_critical_css() {
        // This is a basic implementation - in production you'd want more sophisticated CSS analysis
        return 'body{margin:0;padding:0}h1,h2,h3{margin-top:0}.header,.nav,.hero{display:block}';
    }

    /**
     * Get page identifier from URL
     */
    private function get_page_identifier_from_url($url) {
        $path = parse_url($url, PHP_URL_PATH);
        return md5($path);
    }

    /**
     * Force optimization of all assets (for manual optimization)
     */
    public function force_optimize_all_assets() {
        // Clear existing cache to force re-optimization
        $this->clear_all_cache();

        // Get common WordPress assets for optimization
        $optimized_count = 0;
        $total_savings = 0;

        try {
            // Optimize common CSS files if minification is enabled
            if ($this->settings['minify_css']) {
                $css_files = $this->get_common_css_files();
                foreach ($css_files as $css_file) {
                    if ($this->manually_optimize_css_file($css_file)) {
                        $optimized_count++;
                    }
                }
            }

            // Optimize common JS files if minification is enabled
            if ($this->settings['minify_js']) {
                $js_files = $this->get_common_js_files();
                foreach ($js_files as $js_file) {
                    if ($this->manually_optimize_js_file($js_file)) {
                        $optimized_count++;
                    }
                }
            }

            // Generate critical CSS if enabled
            if ($this->settings['critical_css']) {
                $this->generate_critical_css_for_url(home_url());
            }

            // Update last optimization time
            $stats = get_option('redco_asset_optimization_stats', array());
            $stats['last_optimized'] = time();
            update_option('redco_asset_optimization_stats', $stats);

            return true;

        } catch (Exception $e) {
            error_log('Asset Optimization Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get common CSS files for optimization
     */
    private function get_common_css_files() {
        $css_files = array();

        // Get WordPress core CSS files
        $wp_includes_css = array(
            ABSPATH . 'wp-includes/css/dashicons.min.css',
            ABSPATH . 'wp-includes/css/admin-bar.min.css'
        );

        // Get active theme CSS files
        $theme_css = get_stylesheet_directory() . '/style.css';
        if (file_exists($theme_css)) {
            $css_files[] = $theme_css;
        }

        // Add existing files only
        foreach ($wp_includes_css as $css_file) {
            if (file_exists($css_file)) {
                $css_files[] = $css_file;
            }
        }

        return $css_files;
    }

    /**
     * Get common JS files for optimization
     */
    private function get_common_js_files() {
        $js_files = array();

        // Get WordPress core JS files (avoid jQuery core as it's in exclusion list)
        $wp_includes_js = array(
            ABSPATH . 'wp-includes/js/wp-embed.min.js',
            ABSPATH . 'wp-includes/js/comment-reply.min.js'
        );

        // Add existing files only
        foreach ($wp_includes_js as $js_file) {
            if (file_exists($js_file)) {
                $js_files[] = $js_file;
            }
        }

        return $js_files;
    }

    /**
     * Manually optimize CSS file (for force optimization)
     */
    private function manually_optimize_css_file($file_path) {
        if (!file_exists($file_path) || !is_readable($file_path)) {
            return false;
        }

        try {
            $content = file_get_contents($file_path);
            if ($content === false) {
                return false;
            }

            $original_size = strlen($content);

            // Simple CSS minification
            $minified = $this->minify_css_content($content);
            $optimized_size = strlen($minified);

            // Create optimized cache file
            $cache_dir = $this->cache_dir . 'optimized/';
            if (!file_exists($cache_dir)) {
                wp_mkdir_p($cache_dir);
            }
            $cache_file = $cache_dir . 'css_' . md5($file_path) . '.css';

            if (file_put_contents($cache_file, $minified) !== false) {
                // Update statistics using existing method
                $this->update_optimization_stats($file_path, $original_size, $optimized_size, 'css');
                return true;
            }
        } catch (Exception $e) {
            error_log('CSS Optimization Error: ' . $e->getMessage());
        }

        return false;
    }

    /**
     * Manually optimize JS file (for force optimization)
     */
    private function manually_optimize_js_file($file_path) {
        if (!file_exists($file_path) || !is_readable($file_path)) {
            return false;
        }

        try {
            $content = file_get_contents($file_path);
            if ($content === false) {
                return false;
            }

            $original_size = strlen($content);

            // Simple JS minification
            $minified = $this->minify_js_content($content);
            $optimized_size = strlen($minified);

            // Create optimized cache file
            $cache_dir = $this->cache_dir . 'optimized/';
            if (!file_exists($cache_dir)) {
                wp_mkdir_p($cache_dir);
            }
            $cache_file = $cache_dir . 'js_' . md5($file_path) . '.js';

            if (file_put_contents($cache_file, $minified) !== false) {
                // Update statistics using existing method
                $this->update_optimization_stats($file_path, $original_size, $optimized_size, 'js');
                return true;
            }
        } catch (Exception $e) {
            error_log('JS Optimization Error: ' . $e->getMessage());
        }

        return false;
    }

    /**
     * Simple CSS minification
     */
    private function minify_css_content($css) {
        // Remove comments
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);

        // Remove unnecessary whitespace
        $css = str_replace(array("\r\n", "\r", "\n", "\t", '  ', '    ', '    '), '', $css);

        // Remove whitespace around specific characters
        $css = str_replace(array(' {', '{ ', ' }', '} ', '; ', ' ;', ': ', ' :', ', ', ' ,'), array('{', '{', '}', '}', ';', ';', ':', ':', ',', ','), $css);

        return trim($css);
    }

    /**
     * Simple JS minification
     */
    private function minify_js_content($js) {
        // Remove single-line comments (but preserve URLs)
        $js = preg_replace('/(?<!:)\/\/.*$/m', '', $js);

        // Remove multi-line comments
        $js = preg_replace('/\/\*[\s\S]*?\*\//', '', $js);

        // Remove unnecessary whitespace
        $js = preg_replace('/\s+/', ' ', $js);

        // Remove whitespace around operators
        $js = str_replace(array(' = ', ' + ', ' - ', ' * ', ' / ', ' == ', ' != ', ' < ', ' > ', ' <= ', ' >= '), array('=', '+', '-', '*', '/', '==', '!=', '<', '>', '<=', '>='), $js);

        return trim($js);
    }


}

// Initialize the module
function redco_init_asset_optimization() {
    if (redco_is_module_enabled('asset-optimization')) {
        new Redco_Asset_Optimization();
    }
}
add_action('init', 'redco_init_asset_optimization', 10);

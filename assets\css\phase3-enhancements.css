/**
 * Phase 3: Conservative UI/UX Enhancements
 * Safe improvements that don't break existing layout
 * RedCo Optimizer Plugin
 */

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
/* Reduce paint and layout thrashing */
* {
    box-sizing: border-box;
}

/* Optimize animations for better performance */
@media (prefers-reduced-motion: no-preference) {
    .redco-card,
    .redco-btn,
    .stat-item {
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
/* Better focus management */
.redco-btn:focus,
button:focus,
input:focus,
select:focus,
textarea:focus {
    outline: 2px solid #4CAF50;
    outline-offset: 2px;
}

/* Skip links for keyboard navigation */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #4CAF50;
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10000;
    transition: top 0.3s ease;
}

.skip-link:focus {
    top: 6px;
}

/* ===== RESPONSIVE IMPROVEMENTS ===== */
/* Better mobile experience without breaking layout */
@media (max-width: 768px) {
    /* Improve touch targets */
    .redco-btn,
    button,
    .module-toggle {
        min-height: 44px;
        min-width: 44px;
    }
    
    /* Better spacing on mobile */
    .card-body {
        padding: 16px !important;
    }
    
    /* Improve readability */
    .stat-value {
        font-size: 20px !important;
    }
    
    .stat-label {
        font-size: 12px !important;
    }
}

/* ===== LOADING STATES ===== */
/* Better loading indicators */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid #e3e3e3;
    border-top: 2px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== FORM ENHANCEMENTS ===== */
/* Better form validation feedback */
.form-field-error {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.form-field-success {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

.field-feedback {
    display: block;
    margin-top: 4px;
    font-size: 12px;
}

.field-feedback.error {
    color: #dc3545;
}

.field-feedback.success {
    color: #28a745;
}

/* ===== NOTIFICATION SYSTEM ===== */
/* Non-intrusive notifications */
.redco-notification {
    position: fixed;
    top: 32px;
    right: 20px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 12px 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 300px;
}

.redco-notification.show {
    transform: translateX(0);
}

.redco-notification.success {
    border-left: 4px solid #28a745;
}

.redco-notification.error {
    border-left: 4px solid #dc3545;
}

.redco-notification.warning {
    border-left: 4px solid #ffc107;
}

.redco-notification.info {
    border-left: 4px solid #17a2b8;
}

/* ===== BUTTON ENHANCEMENTS ===== */
/* Better button states without breaking existing styles */
.redco-btn:not(:disabled):hover,
.button:not(:disabled):hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.redco-btn:active,
.button:active {
    transform: translateY(0);
}

.redco-btn:disabled,
.button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* ===== CARD ENHANCEMENTS ===== */
/* Subtle hover effects for cards */
.redco-card:hover,
.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

/* ===== STATISTICS ENHANCEMENTS ===== */
/* Better visual feedback for stat updates */
.stat-updated {
    animation: highlight 1s ease-out;
}

@keyframes highlight {
    0% {
        background-color: rgba(76, 175, 80, 0.2);
        transform: scale(1.05);
    }
    100% {
        background-color: transparent;
        transform: scale(1);
    }
}

/* ===== DARK MODE SUPPORT ===== */
/* Basic dark mode support for users who prefer it */
@media (prefers-color-scheme: dark) {
    .redco-notification {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .loading-overlay {
        background: rgba(45, 55, 72, 0.8);
    }
}

/* ===== HIGH CONTRAST SUPPORT ===== */
/* Better accessibility for high contrast mode */
@media (prefers-contrast: high) {
    .redco-btn,
    .button {
        border: 2px solid currentColor;
    }
    
    .redco-card,
    .stat-item {
        border: 2px solid currentColor;
    }
}

/* ===== PRINT STYLES ===== */
/* Better print experience */
@media print {
    .redco-notification,
    .loading-overlay,
    .skip-link {
        display: none !important;
    }
    
    .redco-card,
    .stat-item {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
}

/* ===== SAVE INDICATORS ===== */
/* Visual feedback for auto-save */
.save-indicator {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    margin-left: 8px;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.save-indicator.saving {
    color: #ffc107;
    opacity: 1;
}

.save-indicator.success {
    color: #28a745;
    opacity: 1;
}

.save-indicator.error {
    color: #dc3545;
    opacity: 1;
}

.save-indicator .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

.save-indicator.saving .dashicons {
    animation: spin 1s linear infinite;
}

/* ===== TOOLTIP ENHANCEMENTS ===== */
/* Better tooltips */
.redco-tooltip {
    position: relative;
    cursor: help;
}

.redco-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.redco-tooltip:hover::after {
    opacity: 1;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
/* GPU acceleration for smooth animations */
.redco-card,
.redco-btn,
.stat-item,
.redco-notification {
    will-change: transform;
}

/* Optimize repaints */
.loading-spinner {
    will-change: transform;
}

/* ===== UNIVERSAL BUTTON DASHICON RULE ===== */
/* All buttons with green backgrounds must have white dashicons */
.redco-btn.btn-primary .dashicons,
.redco-btn.btn-primary:hover .dashicons,
.redco-btn.btn-success .dashicons,
.redco-btn.btn-success:hover .dashicons,
.button-primary .dashicons,
.button-primary:hover .dashicons,
.button-primary:focus .dashicons,
.button-primary:active .dashicons {
    color: #ffffff !important;
}

/* ===== REDUCED MOTION SUPPORT ===== */
/* Respect user preferences for reduced motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .redco-card:hover,
    .redco-btn:hover,
    .stat-item:hover {
        transform: none;
    }
}

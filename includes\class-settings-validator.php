<?php
/**
 * Settings Validation and Sanitization for Redco Optimizer
 * 
 * Comprehensive validation system for all module settings
 * 
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Settings_Validator {
    
    /**
     * Validation rules for different data types
     */
    const VALIDATION_RULES = array(
        'boolean' => array('type' => 'boolean', 'default' => false),
        'integer' => array('type' => 'integer', 'min' => 0, 'max' => PHP_INT_MAX),
        'float' => array('type' => 'float', 'min' => 0.0, 'max' => PHP_FLOAT_MAX),
        'string' => array('type' => 'string', 'max_length' => 255),
        'text' => array('type' => 'string', 'max_length' => 65535),
        'url' => array('type' => 'url', 'schemes' => array('http', 'https')),
        'email' => array('type' => 'email'),
        'array' => array('type' => 'array', 'default' => array()),
        'json' => array('type' => 'json', 'default' => '{}')
    );
    
    /**
     * Module-specific validation schemas
     */
    const MODULE_SCHEMAS = array(
        'page-cache' => array(
            'expiration' => array('type' => 'integer', 'min' => 3600, 'max' => 604800, 'default' => 21600),
            'excluded_pages' => array('type' => 'array', 'default' => array()),
            'cache_mobile' => array('type' => 'boolean', 'default' => true),
            'cache_logged_in' => array('type' => 'boolean', 'default' => false)
        ),
        'lazy-load' => array(
            'exclude_featured' => array('type' => 'boolean', 'default' => false),
            'exclude_woocommerce' => array('type' => 'boolean', 'default' => false),
            'exclude_first_images' => array('type' => 'integer', 'min' => 0, 'max' => 10, 'default' => 2),
            'threshold' => array('type' => 'integer', 'min' => 50, 'max' => 1000, 'default' => 200),
            'placeholder' => array('type' => 'string', 'max_length' => 500, 'default' => 'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1 1"%3E%3C/svg%3E')
        ),
        'asset-optimization' => array(
            'minify_css' => array('type' => 'boolean', 'default' => true),
            'minify_js' => array('type' => 'boolean', 'default' => true),
            'minify_inline' => array('type' => 'boolean', 'default' => true),
            'exclude_css' => array('type' => 'array', 'default' => array()),
            'exclude_js' => array('type' => 'array', 'default' => array('jquery-core', 'jquery-migrate')),
            'critical_css' => array('type' => 'boolean', 'default' => true),
            'defer_non_critical' => array('type' => 'boolean', 'default' => true),
            'optimize_js' => array('type' => 'boolean', 'default' => true),
            'optimize_fonts' => array('type' => 'boolean', 'default' => true),
            'resource_hints' => array('type' => 'boolean', 'default' => true),
            'preconnect_google_fonts' => array('type' => 'boolean', 'default' => true),
            'preconnect_analytics' => array('type' => 'boolean', 'default' => true),
            'combine_css' => array('type' => 'boolean', 'default' => false),
            'combine_js' => array('type' => 'boolean', 'default' => false),
            'async_js' => array('type' => 'boolean', 'default' => true),
            'defer_js' => array('type' => 'boolean', 'default' => true),
            'preload_critical' => array('type' => 'boolean', 'default' => true),
            'remove_unused_css' => array('type' => 'boolean', 'default' => false),
            'enable_gzip' => array('type' => 'boolean', 'default' => true),
            'cache_duration' => array('type' => 'integer', 'default' => 86400),
            'enable_brotli' => array('type' => 'boolean', 'default' => false)
        ),
        'smart-webp-conversion' => array(
            'auto_convert_uploads' => array('type' => 'boolean', 'default' => false),
            'quality' => array('type' => 'integer', 'min' => 30, 'max' => 100, 'default' => 85),
            'lossless' => array('type' => 'boolean', 'default' => false),
            'backup_originals' => array('type' => 'boolean', 'default' => true),
            'batch_size' => array('type' => 'integer', 'min' => 1, 'max' => 50, 'default' => 10),
            'convert_thumbnails' => array('type' => 'boolean', 'default' => true),
            'smart_quality' => array('type' => 'boolean', 'default' => true),
            'max_width' => array('type' => 'integer', 'min' => 100, 'max' => 5000, 'default' => 2048),
            'max_height' => array('type' => 'integer', 'min' => 100, 'max' => 5000, 'default' => 2048)
        ),
        'heartbeat-control' => array(
            'admin_heartbeat' => array('type' => 'string', 'allowed' => array('default', 'modify', 'disable'), 'default' => 'modify'),
            'admin_frequency' => array('type' => 'integer', 'min' => 15, 'max' => 300, 'default' => 60),
            'editor_heartbeat' => array('type' => 'string', 'allowed' => array('default', 'modify', 'disable'), 'default' => 'modify'),
            'editor_frequency' => array('type' => 'integer', 'min' => 15, 'max' => 300, 'default' => 30),
            'frontend_heartbeat' => array('type' => 'string', 'allowed' => array('default', 'modify', 'disable'), 'default' => 'disable'),
            'frontend_frequency' => array('type' => 'integer', 'min' => 15, 'max' => 300, 'default' => 60)
        ),
        'diagnostic-autofix' => array(
            'auto_scan_frequency' => array('type' => 'string', 'allowed' => array('daily', 'weekly', 'monthly'), 'default' => 'weekly'),
            'auto_fix_enabled' => array('type' => 'boolean', 'default' => false),
            'backup_before_fix' => array('type' => 'boolean', 'default' => true),
            'emergency_mode_threshold' => array('type' => 'integer', 'min' => 10, 'max' => 90, 'default' => 40),
            'pagespeed_api_key' => array('type' => 'string', 'max_length' => 100, 'default' => ''),
            'notification_email' => array('type' => 'email', 'default' => '')
        ),

        'security' => array(
            'security_level' => array('type' => 'string', 'allowed' => array('low', 'medium', 'high', 'strict'), 'default' => 'low'),
            'max_failed_attempts' => array('type' => 'integer', 'min' => 1, 'max' => 20, 'default' => 10),
            'lockout_duration' => array('type' => 'integer', 'min' => 300, 'max' => 86400, 'default' => 900),
            'enable_file_monitoring' => array('type' => 'boolean', 'default' => false),
            'enable_request_filtering' => array('type' => 'boolean', 'default' => false),
            'enable_admin_protection' => array('type' => 'boolean', 'default' => false),
            'enable_error_logging' => array('type' => 'boolean', 'default' => true),
            'min_log_level' => array('type' => 'string', 'allowed' => array('emergency', 'critical', 'error', 'warning', 'info', 'debug'), 'default' => 'error')
        )
    );
    
    /**
     * Validate and sanitize module settings
     * 
     * @param string $module_key Module identifier
     * @param array $settings Raw settings array
     * @return array Validated and sanitized settings
     */
    public static function validate_module_settings($module_key, $settings) {
        if (!isset(self::MODULE_SCHEMAS[$module_key])) {
            return $settings; // No validation schema, return as-is
        }
        
        $schema = self::MODULE_SCHEMAS[$module_key];
        $validated = array();
        $errors = array();
        
        foreach ($schema as $setting_key => $rules) {
            $value = isset($settings[$setting_key]) ? $settings[$setting_key] : null;
            
            $result = self::validate_setting($setting_key, $value, $rules);
            
            if ($result['valid']) {
                $validated[$setting_key] = $result['value'];
            } else {
                $validated[$setting_key] = $rules['default'];
                $errors[] = $result['error'];
            }
        }
        
        // Log validation errors if any
        if (!empty($errors) && defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Redco Optimizer Settings Validation Errors for ' . $module_key . ': ' . implode(', ', $errors));
        }
        
        return $validated;
    }
    
    /**
     * Validate individual setting
     * 
     * @param string $key Setting key
     * @param mixed $value Setting value
     * @param array $rules Validation rules
     * @return array Validation result
     */
    public static function validate_setting($key, $value, $rules) {
        $result = array(
            'valid' => true,
            'value' => $value,
            'error' => ''
        );
        
        // Handle null values
        if ($value === null || $value === '') {
            $result['value'] = isset($rules['default']) ? $rules['default'] : null;
            return $result;
        }
        
        switch ($rules['type']) {
            case 'boolean':
                $result['value'] = self::sanitize_boolean($value);
                break;
                
            case 'integer':
                $result = self::validate_integer($value, $rules);
                break;
                
            case 'float':
                $result = self::validate_float($value, $rules);
                break;
                
            case 'string':
                $result = self::validate_string($value, $rules);
                break;
                
            case 'url':
                $result = self::validate_url($value, $rules);
                break;
                
            case 'email':
                $result = self::validate_email($value);
                break;
                
            case 'array':
                $result = self::validate_array($value, $rules);
                break;
                
            case 'json':
                $result = self::validate_json($value, $rules);
                break;
                
            default:
                $result['value'] = redco_safe_sanitize_text_field($value);
        }
        
        // Check allowed values
        if (isset($rules['allowed']) && $result['valid']) {
            if (!in_array($result['value'], $rules['allowed'])) {
                $result['valid'] = false;
                $result['error'] = "Value '{$result['value']}' not in allowed values for {$key}";
                $result['value'] = $rules['default'];
            }
        }
        
        return $result;
    }
    
    /**
     * Sanitize boolean value
     */
    private static function sanitize_boolean($value) {
        if (is_bool($value)) {
            return $value;
        }
        
        if (is_numeric($value)) {
            return (int)$value === 1;
        }
        
        if (is_string($value)) {
            return in_array(strtolower($value), array('1', 'true', 'on', 'yes'));
        }
        
        return false;
    }
    
    /**
     * Validate integer value
     */
    private static function validate_integer($value, $rules) {
        $result = array('valid' => true, 'value' => (int)$value, 'error' => '');
        
        if (!is_numeric($value)) {
            $result['valid'] = false;
            $result['error'] = 'Value must be numeric';
            $result['value'] = isset($rules['default']) ? $rules['default'] : 0;
            return $result;
        }
        
        $int_value = (int)$value;
        
        if (isset($rules['min']) && $int_value < $rules['min']) {
            $result['value'] = $rules['min'];
        }
        
        if (isset($rules['max']) && $int_value > $rules['max']) {
            $result['value'] = $rules['max'];
        }
        
        return $result;
    }
    
    /**
     * Validate float value
     */
    private static function validate_float($value, $rules) {
        $result = array('valid' => true, 'value' => (float)$value, 'error' => '');
        
        if (!is_numeric($value)) {
            $result['valid'] = false;
            $result['error'] = 'Value must be numeric';
            $result['value'] = isset($rules['default']) ? $rules['default'] : 0.0;
            return $result;
        }
        
        $float_value = (float)$value;
        
        if (isset($rules['min']) && $float_value < $rules['min']) {
            $result['value'] = $rules['min'];
        }
        
        if (isset($rules['max']) && $float_value > $rules['max']) {
            $result['value'] = $rules['max'];
        }
        
        return $result;
    }
    
    /**
     * Validate string value
     */
    private static function validate_string($value, $rules) {
        $result = array('valid' => true, 'value' => redco_safe_sanitize_text_field($value), 'error' => '');

        if (isset($rules['max_length']) && strlen($result['value']) > $rules['max_length']) {
            $result['value'] = substr($result['value'], 0, $rules['max_length']);
        }

        return $result;
    }
    
    /**
     * Validate URL value
     */
    private static function validate_url($value, $rules) {
        $result = array('valid' => true, 'value' => redco_safe_esc_url_raw($value), 'error' => '');

        if (!filter_var($result['value'], FILTER_VALIDATE_URL)) {
            $result['valid'] = false;
            $result['error'] = 'Invalid URL format';
            $result['value'] = isset($rules['default']) ? $rules['default'] : '';
        }

        return $result;
    }
    
    /**
     * Validate email value
     */
    private static function validate_email($value) {
        $result = array('valid' => true, 'value' => sanitize_email($value), 'error' => '');
        
        if (!is_email($result['value'])) {
            $result['valid'] = false;
            $result['error'] = 'Invalid email format';
            $result['value'] = '';
        }
        
        return $result;
    }
    
    /**
     * Validate array value
     */
    private static function validate_array($value, $rules) {
        $result = array('valid' => true, 'value' => array(), 'error' => '');
        
        if (is_array($value)) {
            $result['value'] = array_map('redco_safe_sanitize_text_field', $value);
        } elseif (is_string($value)) {
            // Try to parse as comma-separated values
            $result['value'] = array_map('trim', explode(',', $value));
            $result['value'] = array_map('redco_safe_sanitize_text_field', $result['value']);
        } else {
            $result['value'] = isset($rules['default']) ? $rules['default'] : array();
        }
        
        return $result;
    }
    
    /**
     * Validate JSON value
     */
    private static function validate_json($value, $rules) {
        $result = array('valid' => true, 'value' => $value, 'error' => '');
        
        if (is_string($value)) {
            $decoded = json_decode($value, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                $result['valid'] = false;
                $result['error'] = 'Invalid JSON format';
                $result['value'] = isset($rules['default']) ? $rules['default'] : '{}';
            }
        } elseif (is_array($value)) {
            $result['value'] = json_encode($value);
        } else {
            $result['value'] = isset($rules['default']) ? $rules['default'] : '{}';
        }
        
        return $result;
    }
    
    /**
     * Get validation schema for a module
     * 
     * @param string $module_key Module identifier
     * @return array|null Validation schema or null if not found
     */
    public static function get_module_schema($module_key) {
        return isset(self::MODULE_SCHEMAS[$module_key]) ? self::MODULE_SCHEMAS[$module_key] : null;
    }
    
    /**
     * Validate all settings for multiple modules
     * 
     * @param array $modules_settings Array of module settings
     * @return array Validated settings
     */
    public static function validate_all_modules($modules_settings) {
        $validated = array();
        
        foreach ($modules_settings as $module_key => $settings) {
            $validated[$module_key] = self::validate_module_settings($module_key, $settings);
        }
        
        return $validated;
    }
}

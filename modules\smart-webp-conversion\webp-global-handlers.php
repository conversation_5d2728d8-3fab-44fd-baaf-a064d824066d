<?php
/**
 * WebP Global Handlers
 *
 * Handles global AJAX requests for WebP conversion module
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Global AJAX handlers for WebP module
add_action('wp_ajax_redco_webp_enhanced_bulk_convert', 'redco_webp_ajax_bulk_convert');
add_action('wp_ajax_redco_webp_enhanced_test', 'redco_webp_ajax_test');
add_action('wp_ajax_redco_webp_enhanced_stats', 'redco_webp_ajax_stats');
add_action('wp_ajax_redco_webp_get_processable_images', 'redco_webp_ajax_get_processable_images');

/**
 * AJAX handler for bulk conversion
 */
function redco_webp_ajax_bulk_convert() {
    global $redco_webp_instance;
    if ($redco_webp_instance && method_exists($redco_webp_instance, 'ajax_enhanced_bulk_convert')) {
        $redco_webp_instance->ajax_enhanced_bulk_convert();
    } else {
        wp_send_json_error(array('message' => 'WebP module not available'));
    }
}

/**
 * AJAX handler for test conversion
 */
function redco_webp_ajax_test() {
    global $redco_webp_instance;
    if ($redco_webp_instance && method_exists($redco_webp_instance, 'ajax_enhanced_test')) {
        $redco_webp_instance->ajax_enhanced_test();
    } else {
        wp_send_json_error(array('message' => 'WebP module not available'));
    }
}

/**
 * AJAX handler for stats
 */
function redco_webp_ajax_stats() {
    global $redco_webp_instance;
    if ($redco_webp_instance && method_exists($redco_webp_instance, 'ajax_enhanced_stats')) {
        $redco_webp_instance->ajax_enhanced_stats();
    } else {
        wp_send_json_error(array('message' => 'WebP module not available'));
    }
}

/**
 * AJAX handler for getting processable images
 */
function redco_webp_ajax_get_processable_images() {
    global $redco_webp_instance;
    if ($redco_webp_instance && method_exists($redco_webp_instance, 'ajax_get_processable_images')) {
        $redco_webp_instance->ajax_get_processable_images();
    } else {
        wp_send_json_error(array('message' => 'WebP module not available'));
    }
}

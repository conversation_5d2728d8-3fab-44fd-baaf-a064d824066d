/**
 * Simplified Settings Layout CSS
 * Redco Optimizer Plugin
 * Card-free design with left-aligned controls and optimized widths
 */

/* CSS Variables */
:root {
    --redco-primary: #4CAF50;
    --redco-primary-dark: #388E3C;
    --redco-border: #e2e8f0;
    --redco-text: #374151;
    --redco-text-light: #6b7280;
    --redco-bg: #ffffff;
    --redco-bg-light: #f9fafb;
    --redco-transition: all 0.2s ease;
    --redco-divider: #f1f5f9;
    --redco-radius: 6px;
}

/* CARD-FREE LAYOUT WITH BRAND COLORS */

/* Settings Page Container - White background with brand theme */
.redco-optimizer-settings {
    background: #ffffff !important;
    min-height: 100vh;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    box-shadow: none !important;
}

/* Enhanced Settings Header - Professional design with brand colors */
.redco-settings-header {
    background: linear-gradient(135deg, var(--redco-primary) 0%, var(--redco-primary-dark) 100%) !important;
    border: none !important;
    border-bottom: none !important;
    padding: 40px 32px !important;
    margin: 0 auto !important;
    margin-bottom: 0 !important;
    max-width: 1000px !important;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.15) !important;
    position: relative !important;
    overflow: hidden !important;
}

/* Background pattern for header */
.redco-settings-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

/* Header content layout */
.settings-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
}

.settings-title-section {
    flex: 1;
}

.redco-settings-header h1 {
    font-size: 28px;
    font-weight: 700;
    color: #ffffff;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.redco-settings-header h1 .dashicons {
    font-size: 32px;
    width: 32px;
    height: 32px;
    color: #ffffff !important;
}

.redco-settings-header .settings-subtitle {
    color: rgba(255, 255, 255, 0.85);
    margin: 0;
    font-size: 16px;
    line-height: 1.5;
    max-width: 600px;
}

/* Header actions styling */
.settings-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.settings-actions .button {
    background: rgba(255, 255, 255, 0.15);
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 10px 16px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 8px;
}

.settings-actions .button:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.3);
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.settings-actions .button .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Navigation Tabs - With top and bottom borders, match page width */
.redco-nav-tab-wrapper {
    background: #ffffff !important;
    border-top: 1px solid var(--redco-border) !important;
    border-bottom: 1px solid var(--redco-border) !important;
    padding: 0 32px !important;
    margin: 0 auto !important;
    max-width: 1000px !important;
    display: flex;
    gap: 0;
    box-shadow: none !important;
    position: relative;
    z-index: 1;
}

.redco-nav-tab {
    padding: 16px 24px;
    background: transparent !important;
    border: none !important;
    border-bottom: 3px solid transparent !important;
    color: var(--redco-text-light);
    text-decoration: none;
    font-weight: 400;
    transition: var(--redco-transition);
    cursor: pointer;
    box-shadow: none !important;
}

.redco-nav-tab:hover {
    color: var(--redco-text);
    background: var(--redco-bg-light) !important;
    border: none !important;
    box-shadow: none !important;
}

/* Active tab styling - using correct class name */
.redco-nav-tab.redco-nav-tab-active,
.redco-nav-tab.nav-tab-active {
    color: var(--redco-primary) !important;
    background: #ffffff !important;
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
    border-bottom: none !important;
    box-shadow: none !important;
    position: relative;
    z-index: 2;
    font-weight: 700 !important;
    text-decoration: none !important;
}

/* Remove underline from dashicons in active tabs */
.redco-nav-tab.redco-nav-tab-active .dashicons,
.redco-nav-tab.nav-tab-active .dashicons {
    text-decoration: none !important;
}

/* Add underline below tab title text only */
.redco-nav-tab.redco-nav-tab-active .tab-title,
.redco-nav-tab.nav-tab-active .tab-title {
    text-decoration: underline !important;
    text-decoration-color: var(--redco-primary) !important;
    text-decoration-thickness: 2px !important;
    text-underline-offset: 4px !important;
}

/* Settings Content - White background, reduced width, increased padding */
.redco-settings-content {
    padding: 48px 90px;
    margin: 0 auto;
    min-width: 700px;
    background: #ffffff !important;
    border: none !important;
    box-shadow: none !important;
    max-width: 1000px;
}

/* Settings Section - Clean with horizontal line below header, increased spacing */
.redco-settings-section {
    margin-bottom: 64px;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
}

.settings-section-header {
    margin-bottom: 40px;
    background: transparent !important;
    border: none !important;
    border-bottom: 1px solid var(--redco-border) !important;
    box-shadow: none !important;
    padding: 0 0 24px 0 !important;
}

.settings-section-header h2 {
    font-size: 20px;
    font-weight: 600;
    color: var(--redco-text);
    margin: 0 0 8px 0;
}

.settings-section-header p {
    color: var(--redco-text-light);
    margin: 0;
    font-size: 14px;
}

/* FORCE REMOVE ALL CARD STYLING - Override any other CSS files */
.settings-card,
.settings-cards-grid,
.settings-card-header,
.settings-card-content,
.redco-card,
.card-header,
.setup-wizard-card,
.wizard-card-content {
    background: transparent !important;
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Remove any container backgrounds or borders */
.redco-optimizer-settings .settings-card,
.redco-optimizer-settings .settings-cards-grid,
.redco-optimizer-settings .redco-card {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
}

/* Remove hover effects on cards */
.settings-card:hover,
.redco-card:hover {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
}

/* Settings Form - Completely transparent */
.redco-settings-form {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

/* Setting Items - Left-aligned controls with horizontal dividers, increased spacing */
.setting-item {
    display: flex;
    align-items: flex-start;
    gap: 32px;
    padding: 32px 0;
    border-bottom: 1px solid var(--redco-divider);
}

.setting-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.setting-item:first-child {
    padding-top: 0;
}

/* Control on the left */
.setting-control {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    order: 1;
}

/* Info text on the right */
.setting-info {
    flex: 0 1 45%;
    max-width: 450px;
    min-width: 0;
    order: 2;
}

.setting-info h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--redco-text);
    margin: 0 0 6px 0;
    line-height: 1.4;
}

.setting-info p {
    color: var(--redco-text-light);
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
}

/* Hide extra elements for clean design */
.setting-warning,
.setting-benefits,
.benefit-item,
.setting-recommendation,
.setting-note {
    display: none !important;
}

/* Checkbox - Brand colored with proper styling */
.redco-checkbox {
    position: relative;
    display: inline-block;
    width: 20px;
    height: 20px;
}

.redco-checkbox input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.checkbox-custom {
    position: absolute;
    top: 0;
    left: 0;
    width: 20px;
    height: 20px;
    background-color: #ffffff;
    border: 2px solid var(--redco-border);
    border-radius: 4px;
    cursor: pointer;
    transition: var(--redco-transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.checkbox-custom:before {
    content: "";
    width: 10px;
    height: 6px;
    border: 2px solid #ffffff;
    border-top: none;
    border-right: none;
    transform: rotate(-45deg);
    opacity: 0;
    transition: var(--redco-transition);
}

.redco-checkbox input[type="checkbox"]:checked + .checkbox-custom {
    background-color: var(--redco-primary);
    border-color: var(--redco-primary);
}

.redco-checkbox input[type="checkbox"]:checked + .checkbox-custom:before {
    opacity: 1;
}

.redco-checkbox input[type="checkbox"]:focus + .checkbox-custom {
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

.redco-checkbox input[type="checkbox"]:hover + .checkbox-custom {
    border-color: var(--redco-primary);
}

/* Special layout for dropdown, text input, and number input settings - vertical stacking, increased spacing */
.setting-item-dropdown {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
}

.setting-item-dropdown .setting-control {
    order: 1;
    align-self: flex-start;
    flex-shrink: 0;
}

.setting-item-dropdown .setting-info {
    order: 2;
    flex: none;
    width: 90%;
    max-width: none;
}

/* Special styling for Google API setting - info card style */
.google-api-setting .setting-info {
    background: #e8f4fd;
    padding: 16px;
    border-radius: var(--redco-radius);
    border-left: 4px solid var(--redco-primary);
    border: 1px solid #d1e7dd;
}

/* Run Setup Wizard button styling */
.wizard-actions .button-primary .dashicons {
    color: #ffffff !important;
}

/* Security Statistics - Simplified cards in one row, increased spacing */
.security-stats-section {
    margin-top: 32px;
    margin-bottom: 48px;
}

.security-stats-section h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--redco-text);
    margin: 0 0 16px 0;
}

.security-stats-row {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.security-stat-card {
    flex: 1;
    min-width: 150px;
    background: #ffffff;
    border: 1px solid var(--redco-border);
    border-radius: var(--redco-radius);
    padding: 16px;
    text-align: center;
    transition: var(--redco-transition);
}

.security-stat-card:hover {
    border-color: var(--redco-primary);
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.1);
}

.security-stat-card .stat-value {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: var(--redco-primary);
    margin-bottom: 4px;
    line-height: 1.2;
}

.security-stat-card .stat-title {
    display: block;
    font-size: 13px;
    font-weight: 500;
    color: var(--redco-text-light);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Form Controls - Optimized widths */
.redco-select {
    padding: 8px 12px;
    border: 1px solid var(--redco-border);
    border-radius: var(--redco-radius);
    font-size: 14px;
    color: var(--redco-text);
    background: var(--redco-bg);
    transition: var(--redco-transition);
    min-width: 180px;
    max-width: 300px;
}

.redco-text-input {
    padding: 8px 12px;
    border: 1px solid var(--redco-border);
    border-radius: var(--redco-radius);
    font-size: 14px;
    color: var(--redco-text);
    background: var(--redco-bg);
    transition: var(--redco-transition);
    width: 300px;
    max-width: 100%;
}

.redco-number-input {
    padding: 8px 12px;
    border: 1px solid var(--redco-border);
    border-radius: var(--redco-radius);
    font-size: 14px;
    color: var(--redco-text);
    background: var(--redco-bg);
    transition: var(--redco-transition);
    width: 120px;
}

.redco-select:focus,
.redco-text-input:focus,
.redco-number-input:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}



/* Role Selection Grid */
.role-selection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 12px;
    max-width: 500px;
}

.role-checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.role-checkbox-item input[type="checkbox"] {
    margin: 0;
}

.role-checkbox-item label {
    font-size: 14px;
    color: var(--redco-text);
    cursor: pointer;
}



/* Responsive Design - Maintain increased spacing on mobile */
@media (max-width: 768px) {
    .redco-settings-header,
    .redco-nav-tab-wrapper {
        padding-left: 16px;
        padding-right: 16px;
    }

    .redco-settings-content {
        padding: 32px 16px;
    }

    .setting-item {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
        padding: 24px 0;
    }

    .setting-control {
        align-self: flex-start;
        order: 2;
    }

    .setting-info {
        order: 1;
    }

    .redco-text-input {
        width: 100%;
    }

    .redco-nav-tab-wrapper {
        flex-wrap: wrap;
    }
}

/* Hide WordPress default form table */
.redco-settings-form table.form-table {
    display: none;
}

/* OVERRIDE ANY WORDPRESS ADMIN STYLING */
.wrap .redco-optimizer-settings,
.wrap .redco-settings-section {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    margin-bottom: 64px !important;
}

/* Settings section header - preserve the border-bottom, increased spacing */
.wrap .settings-section-header {
    background: transparent !important;
    box-shadow: none !important;
    padding: 0 0 24px 0 !important;
    margin-bottom: 40px !important;
    border: none !important;
    border-bottom: 1px solid var(--redco-border) !important;
}

/* Remove any WordPress notice styling conflicts */
.wrap .notice + .redco-optimizer-settings,
.wrap .updated + .redco-optimizer-settings,
.wrap .error + .redco-optimizer-settings {
    margin-top: 0 !important;
    background: transparent !important;
    border: none !important;
}

/* Ensure completely flat layout */
.redco-optimizer-settings *:not(.setting-item):not(.redco-toggle-switch):not(.toggle-slider):not(.redco-select):not(.redco-text-input):not(.redco-number-input) {
    border-radius: 0 !important;
}

/* Remove any remaining container styling */
.redco-optimizer-settings .wrap,
.redco-optimizer-settings .postbox,
.redco-optimizer-settings .inside {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
}

/* Brand Color for All Dashicons */
.redco-optimizer-settings .dashicons,
.redco-optimizer-settings .dashicons-before:before,
.redco-nav-tab .dashicons,
.redco-settings-header .dashicons,
.settings-section-header .dashicons,
.wizard-info .dashicons,
.button .dashicons {
    color: var(--redco-primary) !important;
}

/* Specific dashicon styling for tabs */
.redco-nav-tab .dashicons {
    margin-right: 8px;
    font-size: 16px;
    width: 16px;
    height: 16px;
    vertical-align: middle;
}

/* Header dashicons */
.redco-settings-header .dashicons {
    font-size: 24px;
    width: 24px;
    height: 24px;
}

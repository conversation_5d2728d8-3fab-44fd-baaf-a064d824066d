<?php
/**
 * Heartbeat Control Module Settings
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get module instance
$heartbeat_control = new Redco_Heartbeat_Control();
$is_enabled = redco_is_module_enabled('heartbeat-control');

// Get current settings
$admin_heartbeat = redco_get_module_option('heartbeat-control', 'admin_heartbeat', 'modify');
$admin_frequency = redco_get_module_option('heartbeat-control', 'admin_frequency', 60);
$editor_heartbeat = redco_get_module_option('heartbeat-control', 'editor_heartbeat', 'modify');
$editor_frequency = redco_get_module_option('heartbeat-control', 'editor_frequency', 30);
$frontend_heartbeat = redco_get_module_option('heartbeat-control', 'frontend_heartbeat', 'disable');
$frontend_frequency = redco_get_module_option('heartbeat-control', 'frontend_frequency', 60);

// Get heartbeat statistics - only if module is enabled
$stats = array(
    'admin_status' => $admin_heartbeat,
    'admin_frequency' => $admin_frequency,
    'editor_status' => $editor_heartbeat,
    'editor_frequency' => $editor_frequency,
    'frontend_status' => $frontend_heartbeat,
    'frontend_frequency' => $frontend_frequency
);
if ($is_enabled && class_exists('Redco_Heartbeat_Control')) {
    $stats = $heartbeat_control->get_stats();
}

// Create settings array for shared optimization panel
$settings = array(
    'admin_heartbeat' => $admin_heartbeat,
    'admin_frequency' => $admin_frequency,
    'editor_heartbeat' => $editor_heartbeat,
    'editor_frequency' => $editor_frequency,
    'frontend_heartbeat' => $frontend_heartbeat,
    'frontend_frequency' => $frontend_frequency,
    'frequencies_customized' => redco_get_module_option('heartbeat-control', 'frequencies_customized', false)
);
?>

<div class="redco-module-tab" data-module="heartbeat-control">
    <!-- Enhanced Professional Header Section -->
    <div class="module-header-section">
        <!-- Breadcrumb Navigation -->
        <div class="header-breadcrumb">
            <div class="breadcrumb-nav">
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer'); ?>">
                    <span class="dashicons dashicons-performance"></span>
                    <?php _e('Redco Optimizer', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>">
                    <?php _e('Modules', 'redco-optimizer'); ?>
                </a>
                <span class="breadcrumb-separator">›</span>
                <span class="breadcrumb-current"><?php _e('Heartbeat Control', 'redco-optimizer'); ?></span>
            </div>
        </div>

        <!-- Main Header Content -->
        <div class="header-content">
            <div class="header-main">
                <div class="header-icon">
                    <span class="dashicons dashicons-heart"></span>
                </div>
                <div class="header-text">
                    <h1><?php _e('Heartbeat Control', 'redco-optimizer'); ?></h1>
                    <p><?php _e('Control WordPress Heartbeat API frequency to reduce server load and improve performance', 'redco-optimizer'); ?></p>

                    <!-- Status Indicators -->
                    <div class="header-status">
                        <?php if ($is_enabled): ?>
                            <div class="status-badge enabled">
                                <span class="dashicons dashicons-yes-alt"></span>
                                <?php _e('Active', 'redco-optimizer'); ?>
                            </div>
                            <?php if ($admin_heartbeat !== 'default'): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-admin-generic"></span>
                                    <?php _e('Admin Optimized', 'redco-optimizer'); ?>
                                </div>
                            <?php endif; ?>
                            <?php if ($frontend_heartbeat === 'disable'): ?>
                                <div class="status-badge performance">
                                    <span class="dashicons dashicons-admin-site"></span>
                                    <?php _e('Frontend Disabled', 'redco-optimizer'); ?>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <div class="status-badge disabled">
                                <span class="dashicons dashicons-warning"></span>
                                <?php _e('Inactive', 'redco-optimizer'); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Header Actions -->
            <div class="header-actions">
                <div class="header-action-group">
                    <!-- Quick Actions -->
                    <div class="header-quick-actions">
                        <?php if ($is_enabled): ?>
                            <button type="button" class="header-action-btn" id="apply-recommended-heartbeat">
                                <span class="dashicons dashicons-yes"></span>
                                <?php _e('Recommended', 'redco-optimizer'); ?>
                            </button>
                            <button type="button" class="header-action-btn" id="reset-heartbeat-settings">
                                <span class="dashicons dashicons-undo"></span>
                                <?php _e('Reset', 'redco-optimizer'); ?>
                            </button>
                            <a href="<?php echo admin_url('admin.php?page=redco-optimizer&tab=diagnostic-autofix'); ?>" class="header-action-btn">
                                <span class="dashicons dashicons-admin-tools"></span>
                                <?php _e('Diagnose', 'redco-optimizer'); ?>
                            </a>
                        <?php endif; ?>
                        <a href="<?php echo admin_url('admin.php?page=redco-optimizer-modules'); ?>" class="header-action-btn">
                            <span class="dashicons dashicons-admin-generic"></span>
                            <?php _e('All Modules', 'redco-optimizer'); ?>
                        </a>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <?php if ($is_enabled): ?>
                    <div class="header-metrics">
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo $admin_heartbeat === 'modify' ? $admin_frequency . 's' : ucfirst($admin_heartbeat); ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Admin', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo $editor_heartbeat === 'modify' ? $editor_frequency . 's' : ucfirst($editor_heartbeat); ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Editor', 'redco-optimizer'); ?></div>
                        </div>
                        <div class="header-metric">
                            <div class="header-metric-value">
                                <?php echo $frontend_heartbeat === 'disable' ? '✓' : '✗'; ?>
                            </div>
                            <div class="header-metric-label"><?php _e('Frontend', 'redco-optimizer'); ?></div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php if ($is_enabled): ?>
    <!-- Module Content -->
    <div class="redco-module-content">
        <div class="module-layout">
            <div class="redco-content-main">
                <form class="redco-module-form" data-module="heartbeat-control">
                    <!-- Heartbeat Control Configuration -->
                    <div class="redco-card">
                        <div class="card-header">
                            <h3>
                                <span class="dashicons dashicons-heart"></span>
                                <?php _e('WordPress Heartbeat Configuration', 'redco-optimizer'); ?>
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="settings-intro">
                                <p class="description">
                                    <?php _e('WordPress Heartbeat API provides real-time communication between your browser and server. Control its frequency to balance functionality with performance.', 'redco-optimizer'); ?>
                                </p>
                                <div class="performance-indicator">
                                    <span class="indicator-label"><?php _e('Current Performance Impact:', 'redco-optimizer'); ?></span>
                                    <span class="impact-level medium" id="heartbeat-impact"><?php _e('Medium', 'redco-optimizer'); ?></span>
                                </div>
                            </div>

                            <div class="heartbeat-sections">
                                <!-- Admin Area Section -->
                                <div class="heartbeat-section">
                                    <h4 class="section-title">
                                        <span class="dashicons dashicons-admin-generic"></span>
                                        <?php _e('Admin Area Heartbeat', 'redco-optimizer'); ?>
                                    </h4>

                                    <div class="heartbeat-control-group">
                                        <div class="control-main">
                                            <label for="admin_heartbeat" class="control-label">
                                                <strong><?php _e('Admin Area Control', 'redco-optimizer'); ?></strong>
                                                <span class="control-description"><?php _e('Dashboard, settings, and admin pages', 'redco-optimizer'); ?></span>
                                            </label>
                                            <select name="settings[admin_heartbeat]" id="admin_heartbeat" class="heartbeat-control-select">
                                                <option value="default" <?php selected($admin_heartbeat, 'default'); ?> data-impact="medium"><?php _e('Default (15 seconds)', 'redco-optimizer'); ?></option>
                                                <option value="modify" <?php selected($admin_heartbeat, 'modify'); ?> data-impact="low"><?php _e('Custom Frequency', 'redco-optimizer'); ?></option>
                                                <option value="disable" <?php selected($admin_heartbeat, 'disable'); ?> data-impact="high"><?php _e('Disable Completely', 'redco-optimizer'); ?></option>
                                            </select>
                                        </div>

                                        <div class="frequency-control" data-parent="admin_heartbeat" data-show-when="modify">
                                            <label for="admin_frequency" class="frequency-label"><?php _e('Custom Frequency:', 'redco-optimizer'); ?></label>
                                            <select name="settings[admin_frequency]" id="admin_frequency" class="frequency-select">
                                                <option value="15" <?php selected($admin_frequency, 15); ?>><?php _e('15 seconds (Default)', 'redco-optimizer'); ?></option>
                                                <option value="30" <?php selected($admin_frequency, 30); ?>><?php _e('30 seconds', 'redco-optimizer'); ?></option>
                                                <option value="60" <?php selected($admin_frequency, 60); ?>><?php _e('60 seconds (Recommended)', 'redco-optimizer'); ?></option>
                                                <option value="120" <?php selected($admin_frequency, 120); ?>><?php _e('2 minutes', 'redco-optimizer'); ?></option>
                                                <option value="300" <?php selected($admin_frequency, 300); ?>><?php _e('5 minutes', 'redco-optimizer'); ?></option>
                                            </select>
                                        </div>

                                        <div class="control-info">
                                            <div class="info-item">
                                                <span class="info-label"><?php _e('Used for:', 'redco-optimizer'); ?></span>
                                                <span class="info-value"><?php _e('Dashboard widgets, notifications, user activity', 'redco-optimizer'); ?></span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label"><?php _e('Recommendation:', 'redco-optimizer'); ?></span>
                                                <span class="info-value recommended"><?php _e('60 seconds for better performance', 'redco-optimizer'); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Post Editor Section -->
                                <div class="heartbeat-section">
                                    <h4 class="section-title">
                                        <span class="dashicons dashicons-edit"></span>
                                        <?php _e('Post Editor Heartbeat', 'redco-optimizer'); ?>
                                    </h4>

                                    <div class="heartbeat-control-group">
                                        <div class="control-main">
                                            <label for="editor_heartbeat" class="control-label">
                                                <strong><?php _e('Editor Control', 'redco-optimizer'); ?></strong>
                                                <span class="control-description"><?php _e('Post/page editor autosave and user activity', 'redco-optimizer'); ?></span>
                                            </label>
                                            <select name="settings[editor_heartbeat]" id="editor_heartbeat" class="heartbeat-control-select">
                                                <option value="default" <?php selected($editor_heartbeat, 'default'); ?> data-impact="medium"><?php _e('Default (15 seconds)', 'redco-optimizer'); ?></option>
                                                <option value="modify" <?php selected($editor_heartbeat, 'modify'); ?> data-impact="low"><?php _e('Custom Frequency', 'redco-optimizer'); ?></option>
                                                <option value="disable" <?php selected($editor_heartbeat, 'disable'); ?> data-impact="high"><?php _e('Disable (Not Recommended)', 'redco-optimizer'); ?></option>
                                            </select>
                                        </div>

                                        <div class="frequency-control" data-parent="editor_heartbeat" data-show-when="modify">
                                            <label for="editor_frequency" class="frequency-label"><?php _e('Custom Frequency:', 'redco-optimizer'); ?></label>
                                            <select name="settings[editor_frequency]" id="editor_frequency" class="frequency-select">
                                                <option value="15" <?php selected($editor_frequency, 15); ?>><?php _e('15 seconds (Default)', 'redco-optimizer'); ?></option>
                                                <option value="30" <?php selected($editor_frequency, 30); ?>><?php _e('30 seconds (Recommended)', 'redco-optimizer'); ?></option>
                                                <option value="60" <?php selected($editor_frequency, 60); ?>><?php _e('60 seconds', 'redco-optimizer'); ?></option>
                                                <option value="120" <?php selected($editor_frequency, 120); ?>><?php _e('2 minutes', 'redco-optimizer'); ?></option>
                                            </select>
                                        </div>

                                        <div class="control-info">
                                            <div class="info-item">
                                                <span class="info-label"><?php _e('Used for:', 'redco-optimizer'); ?></span>
                                                <span class="info-value"><?php _e('Autosave, post locks, collaborative editing', 'redco-optimizer'); ?></span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label"><?php _e('Recommendation:', 'redco-optimizer'); ?></span>
                                                <span class="info-value recommended"><?php _e('30 seconds for balanced autosave protection', 'redco-optimizer'); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Frontend Section -->
                                <div class="heartbeat-section">
                                    <h4 class="section-title">
                                        <span class="dashicons dashicons-admin-site"></span>
                                        <?php _e('Frontend Heartbeat', 'redco-optimizer'); ?>
                                    </h4>

                                    <div class="heartbeat-control-group">
                                        <div class="control-main">
                                            <label for="frontend_heartbeat" class="control-label">
                                                <strong><?php _e('Frontend Control', 'redco-optimizer'); ?></strong>
                                                <span class="control-description"><?php _e('Public pages and visitor experience', 'redco-optimizer'); ?></span>
                                            </label>
                                            <select name="settings[frontend_heartbeat]" id="frontend_heartbeat" class="heartbeat-control-select">
                                                <option value="default" <?php selected($frontend_heartbeat, 'default'); ?> data-impact="high"><?php _e('Default (15 seconds)', 'redco-optimizer'); ?></option>
                                                <option value="modify" <?php selected($frontend_heartbeat, 'modify'); ?> data-impact="medium"><?php _e('Custom Frequency', 'redco-optimizer'); ?></option>
                                                <option value="disable" <?php selected($frontend_heartbeat, 'disable'); ?> data-impact="low"><?php _e('Disable (Recommended)', 'redco-optimizer'); ?></option>
                                            </select>
                                        </div>

                                        <div class="frequency-control" data-parent="frontend_heartbeat" data-show-when="modify">
                                            <label for="frontend_frequency" class="frequency-label"><?php _e('Custom Frequency:', 'redco-optimizer'); ?></label>
                                            <select name="settings[frontend_frequency]" id="frontend_frequency" class="frequency-select">
                                                <option value="30" <?php selected($frontend_frequency, 30); ?>><?php _e('30 seconds', 'redco-optimizer'); ?></option>
                                                <option value="60" <?php selected($frontend_frequency, 60); ?>><?php _e('60 seconds', 'redco-optimizer'); ?></option>
                                                <option value="120" <?php selected($frontend_frequency, 120); ?>><?php _e('2 minutes', 'redco-optimizer'); ?></option>
                                                <option value="300" <?php selected($frontend_frequency, 300); ?>><?php _e('5 minutes', 'redco-optimizer'); ?></option>
                                            </select>
                                        </div>

                                        <div class="control-info">
                                            <div class="info-item">
                                                <span class="info-label"><?php _e('Used for:', 'redco-optimizer'); ?></span>
                                                <span class="info-value"><?php _e('Usually not needed for regular visitors', 'redco-optimizer'); ?></span>
                                            </div>
                                            <div class="info-item">
                                                <span class="info-label"><?php _e('Recommendation:', 'redco-optimizer'); ?></span>
                                                <span class="info-value recommended"><?php _e('Disable for maximum performance', 'redco-optimizer'); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="heartbeat-summary">
                                <div class="summary-stats">
                                    <span class="performance-savings"><?php _e('Estimated performance gain: Medium', 'redco-optimizer'); ?></span>
                                    <span class="server-load"><?php _e('Server load reduction: Moderate', 'redco-optimizer'); ?></span>
                                </div>
                                <div class="summary-notice">
                                    <span class="dashicons dashicons-info"></span>
                                    <?php _e('Changes take effect immediately. Test your admin functionality after making changes.', 'redco-optimizer'); ?>
                                </div>
                            </div>
                        </div>
                    </div>

                </form>
            </div>

            <!-- Sidebar -->
            <div class="redco-content-sidebar">
                <!-- Module Statistics -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-chart-bar"></span>
                            <?php _e('Current Settings', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <div class="stats-grid">
                            <div class="stat-item stat-admin">
                                <span class="stat-value status-<?php echo esc_attr($stats['admin_status']); ?>">
                                    <?php echo esc_html(ucfirst($stats['admin_status'])); ?>
                                    <?php if ($stats['admin_status'] === 'modify'): ?>
                                        (<?php echo $stats['admin_frequency']; ?>s)
                                    <?php endif; ?>
                                </span>
                                <span class="stat-label"><?php _e('Admin Status', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-editor">
                                <span class="stat-value status-<?php echo esc_attr($stats['editor_status']); ?>">
                                    <?php echo esc_html(ucfirst($stats['editor_status'])); ?>
                                    <?php if ($stats['editor_status'] === 'modify'): ?>
                                        (<?php echo $stats['editor_frequency']; ?>s)
                                    <?php endif; ?>
                                </span>
                                <span class="stat-label"><?php _e('Editor Status', 'redco-optimizer'); ?></span>
                            </div>
                            <div class="stat-item stat-frontend">
                                <span class="stat-value status-<?php echo esc_attr($stats['frontend_status']); ?>">
                                    <?php echo esc_html(ucfirst($stats['frontend_status'])); ?>
                                    <?php if ($stats['frontend_status'] === 'modify'): ?>
                                        (<?php echo $stats['frontend_frequency']; ?>s)
                                    <?php endif; ?>
                                </span>
                                <span class="stat-label"><?php _e('Frontend Status', 'redco-optimizer'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Phase 3 Consolidation: Shared Optimization Panel -->
                <?php
                if (class_exists('Redco_Shared_Optimization_Settings')) {
                    Redco_Shared_Optimization_Settings::render_performance_optimization_panel('heartbeat-control', $settings);
                }
                ?>

                <!-- Performance Impact -->
                <div class="redco-sidebar-section">
                    <div class="sidebar-section-header">
                        <h3>
                            <span class="dashicons dashicons-performance"></span>
                            <?php _e('Performance Impact', 'redco-optimizer'); ?>
                        </h3>
                    </div>
                    <div class="sidebar-section-content">
                        <p><strong><?php _e('Heartbeat API is used for:', 'redco-optimizer'); ?></strong></p>
                        <ul style="margin: 10px 0; padding-left: 20px; font-size: 12px;">
                            <li><?php _e('Autosaving posts and pages while editing', 'redco-optimizer'); ?></li>
                            <li><?php _e('Showing user activity and post locks', 'redco-optimizer'); ?></li>
                            <li><?php _e('Displaying notifications and updates', 'redco-optimizer'); ?></li>
                            <li><?php _e('Real-time features in admin area', 'redco-optimizer'); ?></li>
                        </ul>
                        <p><strong><?php _e('Recommendations:', 'redco-optimizer'); ?></strong></p>
                        <ul style="margin: 10px 0; padding-left: 20px; font-size: 12px;">
                            <li><?php _e('Disable on frontend (visitors don\'t need it)', 'redco-optimizer'); ?></li>
                            <li><?php _e('Reduce frequency in admin area (60 seconds is usually fine)', 'redco-optimizer'); ?></li>
                            <li><?php _e('Keep default or 30 seconds in post editor for better autosave', 'redco-optimizer'); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
    <!-- Module Disabled State -->
    <div class="redco-module-disabled">
        <div class="disabled-message">
            <span class="dashicons dashicons-heart"></span>
            <h3><?php _e('Heartbeat Control Module Disabled', 'redco-optimizer'); ?></h3>
            <p><?php _e('This module is currently disabled. Enable it from the Modules page to control WordPress Heartbeat API frequency.', 'redco-optimizer'); ?></p>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Phase 3 Consolidation: Shared Optimization JavaScript -->
<?php
if (class_exists('Redco_Shared_Optimization_Settings')) {
    Redco_Shared_Optimization_Settings::render_optimization_javascript();
}
?>

<script>
jQuery(document).ready(function($) {
    // Enhanced Heartbeat Control Functionality

    // Show/hide frequency options based on heartbeat control selection
    function toggleFrequencyOptions() {
        $('.frequency-control').each(function() {
            const $control = $(this);
            const parent = $control.data('parent');
            const showWhen = $control.data('show-when');
            const parentValue = $('#' + parent).val();

            if (parentValue === showWhen) {
                $control.slideDown(200);
            } else {
                $control.slideUp(200);
            }
        });
    }

    // Update performance impact indicator
    function updatePerformanceImpact() {
        const adminImpact = $('#admin_heartbeat option:selected').data('impact');
        const editorImpact = $('#editor_heartbeat option:selected').data('impact');
        const frontendImpact = $('#frontend_heartbeat option:selected').data('impact');

        // Calculate overall impact
        let overallImpact = 'low';
        const impacts = [adminImpact, editorImpact, frontendImpact];

        if (impacts.includes('high')) {
            overallImpact = 'high';
        } else if (impacts.includes('medium')) {
            overallImpact = 'medium';
        }

        const impactText = overallImpact === 'high' ? '<?php _e("High Performance", "redco-optimizer"); ?>' :
                          overallImpact === 'medium' ? '<?php _e("Medium Performance", "redco-optimizer"); ?>' :
                          '<?php _e("Low Performance", "redco-optimizer"); ?>';

        $('#heartbeat-impact').text(impactText);
        $('#heartbeat-impact').removeClass('high medium low').addClass(overallImpact);

        // Update summary stats
        updateSummaryStats();
    }

    // Update summary statistics
    function updateSummaryStats() {
        const adminSetting = $('#admin_heartbeat').val();
        const editorSetting = $('#editor_heartbeat').val();
        const frontendSetting = $('#frontend_heartbeat').val();

        let performanceGain = '<?php _e("Low", "redco-optimizer"); ?>';
        let serverReduction = '<?php _e("Minimal", "redco-optimizer"); ?>';

        // Calculate performance gain
        let optimizedCount = 0;
        if (adminSetting === 'modify' || adminSetting === 'disable') optimizedCount++;
        if (editorSetting === 'modify') optimizedCount++;
        if (frontendSetting === 'disable') optimizedCount++;

        if (optimizedCount >= 2) {
            performanceGain = '<?php _e("High", "redco-optimizer"); ?>';
            serverReduction = '<?php _e("Significant", "redco-optimizer"); ?>';
        } else if (optimizedCount === 1) {
            performanceGain = '<?php _e("Medium", "redco-optimizer"); ?>';
            serverReduction = '<?php _e("Moderate", "redco-optimizer"); ?>';
        }

        $('.performance-savings').text('<?php _e("Estimated performance gain:", "redco-optimizer"); ?> ' + performanceGain);
        $('.server-load').text('<?php _e("Server load reduction:", "redco-optimizer"); ?> ' + serverReduction);
    }

    // Apply Recommended Settings button
    $('#apply-recommended-heartbeat').on('click', function(e) {
        e.preventDefault();

        const button = $(this);
        button.addClass('loading').text('<?php _e("Applying...", "redco-optimizer"); ?>');

        setTimeout(() => {
            // Apply recommended settings
            $('#admin_heartbeat').val('modify');
            $('#admin_frequency').val('60');
            $('#editor_heartbeat').val('modify');
            $('#editor_frequency').val('30');
            $('#frontend_heartbeat').val('disable');

            toggleFrequencyOptions();
            updatePerformanceImpact();

            button.removeClass('loading').text('<?php _e("Apply Recommended", "redco-optimizer"); ?>');

            // Use global toast notification system
            if (typeof showToast === 'function') {
                showToast('<?php _e("Recommended settings applied successfully!", "redco-optimizer"); ?>', 'success');
            }

            // Highlight changes
            $('.heartbeat-control-group').addClass('settings-updated');
            setTimeout(() => {
                $('.heartbeat-control-group').removeClass('settings-updated');
            }, 1000);
        }, 500);
    });

    // Reset to Defaults button
    $('#reset-heartbeat-settings').on('click', function(e) {
        e.preventDefault();

        const button = $(this);
        button.addClass('loading').text('<?php _e("Resetting...", "redco-optimizer"); ?>');

        setTimeout(() => {
            // Reset to default settings
            $('#admin_heartbeat').val('default');
            $('#editor_heartbeat').val('default');
            $('#frontend_heartbeat').val('default');

            toggleFrequencyOptions();
            updatePerformanceImpact();

            button.removeClass('loading').text('<?php _e("Reset to Defaults", "redco-optimizer"); ?>');

            showHeartbeatNotification('<?php _e("Settings reset to WordPress defaults", "redco-optimizer"); ?>', 'info');

            // Highlight changes
            $('.heartbeat-control-group').addClass('settings-updated');
            setTimeout(() => {
                $('.heartbeat-control-group').removeClass('settings-updated');
            }, 1000);
        }, 500);
    });

    // Individual control changes
    $('.heartbeat-control-select').on('change', function() {
        toggleFrequencyOptions();
        updatePerformanceImpact();

        // Highlight the changed section
        $(this).closest('.heartbeat-control-group').addClass('settings-changed');
        setTimeout(() => {
            $(this).closest('.heartbeat-control-group').removeClass('settings-changed');
        }, 500);
    });

    // Frequency changes
    $('.frequency-select').on('change', function() {
        updatePerformanceImpact();
    });

    // Page-specific notification system removed - using global toast notifications

    // Add visual feedback for control groups
    $('.heartbeat-control-group').on('mouseenter', function() {
        $(this).addClass('group-hover');
    }).on('mouseleave', function() {
        $(this).removeClass('group-hover');
    });

    // Initialize
    toggleFrequencyOptions();
    updatePerformanceImpact();

    // Heartbeat Control module enhanced functionality loaded
});
</script>

<style>
.status-default { color: #666; }
.status-modify { color: #4CAF50; }
.status-disable { color: #d63638; }
.frequency-row { margin-left: 20px; border-left: 3px solid #4CAF50; padding-left: 15px; }
</style>

/**
 * RedCo Optimizer - Consolidated CSS
 * Unified styles for all modules and admin interface
 * Optimized for performance and consistency
 */

/* ===== CSS VARIABLES - UNIFIED THEME SYSTEM ===== */
:root {
    /* Primary Brand Colors */
    --redco-primary: #4CAF50;
    --redco-primary-dark: #388E3C;
    --redco-primary-light: #66BB6A;
    
    /* Semantic Colors */
    --redco-secondary: #64748b;
    --redco-success: #16a34a;
    --redco-warning: #f59e0b;
    --redco-danger: #dc2626;
    --redco-info: #2563eb;
    
    /* Background Colors */
    --redco-light: #f8fafc;
    --redco-dark: #1e293b;
    --redco-bg-section: #ffffff;
    --redco-bg-card: #ffffff;
    --redco-bg-subtle: #f9fafb;
    
    /* Text Colors */
    --redco-text: #374151;
    --redco-text-light: #6b7280;
    
    /* Border & Shadow */
    --redco-border: #e5e7eb;
    --redco-border-light: #f3f4f6;
    --redco-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --redco-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --redco-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    
    /* Border Radius */
    --redco-radius: 6px;
    --redco-radius-lg: 8px;
    
    /* Transitions */
    --redco-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    
    /* WordPress Admin Bar Height */
    --wp-admin--admin-bar--height: 32px;
}

/* ===== GLOBAL TYPOGRAPHY ===== */
.redco-optimizer-admin,
.redco-module-tab {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    line-height: 1.6;
    color: var(--redco-text);
}

.redco-optimizer-admin h1,
.redco-optimizer-admin h2,
.redco-optimizer-admin h3,
.redco-optimizer-admin h4,
.redco-optimizer-admin h5,
.redco-optimizer-admin h6,
.redco-module-tab h1,
.redco-module-tab h2,
.redco-module-tab h3,
.redco-module-tab h4,
.redco-module-tab h5,
.redco-module-tab h6 {
    font-weight: 600;
    line-height: 1.3;
    color: var(--redco-text);
    margin: 0 0 16px 0;
}

.redco-optimizer-admin p,
.redco-module-tab p {
    line-height: 1.6;
    color: var(--redco-text);
    margin-bottom: 16px;
}

.redco-optimizer-admin small,
.redco-module-tab small {
    color: var(--redco-text-light);
    font-size: 13px;
}

/* ===== MAIN LAYOUT SYSTEM ===== */
.redco-optimizer-admin {
    margin: 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    border-radius: 0;
    overflow: hidden;
    box-shadow: none;
}

.redco-admin-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    grid-template-rows: 1fr;
    gap: 0;
    max-width: 1600px;
    margin: 0 auto;
    padding: 0;
    min-height: calc(100vh - var(--wp-admin--admin-bar--height));
    align-items: stretch;
}

/* ===== MODULE TAB CONTAINER ===== */
.redco-module-tab {
    background: var(--redco-bg-subtle);
    min-height: 100vh;
    padding: 0px 0px 60px 0px;
    margin: 0;
}

/* ===== ENHANCED SIDEBAR SYSTEM ===== */
.redco-sidebar-header {
    background: linear-gradient(135deg, var(--redco-primary) 0%, var(--redco-primary-dark) 100%);
    color: #fff;
    padding: 20px;
    margin: 0;
    border-radius: 0 0 0 16px;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex-shrink: 0;
    min-height: 182px;
}

.redco-sidebar-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

/* ===== ENHANCED CARD SYSTEM ===== */
.redco-card {
    background: var(--redco-bg-card);
    border-radius: var(--redco-radius);
    box-shadow: var(--redco-shadow);
    border: 1px solid var(--redco-border);
    margin-bottom: 32px;
    overflow: hidden;
    transition: var(--redco-transition);
}

.redco-card:hover {
    box-shadow: var(--redco-shadow-md);
    border-color: #d1d5db;
}

.card-header {
    background: var(--redco-bg-card);
    border-bottom: 1px solid var(--redco-border-light);
    padding: 24px 28px 20px 28px;
    position: relative;
}

.card-header h2,
.card-header h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--redco-text);
    line-height: 1.3;
}

.card-header p {
    margin: 0;
    color: var(--redco-text-light);
    font-size: 14px;
    line-height: 1.4;
}

.card-body {
    padding: 28px;
}

.card-footer {
    background: var(--redco-bg-subtle);
    border-top: 1px solid var(--redco-border-light);
    padding: 20px 28px;
}

/* ===== ENHANCED BUTTON SYSTEM ===== */
.redco-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border: 1px solid transparent;
    border-radius: var(--redco-radius);
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--redco-transition);
    background: var(--redco-bg-card);
    color: var(--redco-text);
    box-shadow: var(--redco-shadow);
}

.redco-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--redco-shadow-md);
    text-decoration: none;
}

.redco-btn:active {
    transform: translateY(0);
}

.redco-btn.btn-primary {
    background: var(--redco-primary);
    color: white;
    border-color: var(--redco-primary);
}

.redco-btn.btn-primary:hover {
    background: var(--redco-primary-dark);
    border-color: var(--redco-primary-dark);
    color: white;
}

.redco-btn.btn-secondary {
    background: var(--redco-secondary);
    color: white;
    border-color: var(--redco-secondary);
}

.redco-btn.btn-success {
    background: var(--redco-success);
    color: white;
    border-color: var(--redco-success);
}

.redco-btn.btn-warning {
    background: var(--redco-warning);
    color: white;
    border-color: var(--redco-warning);
}

.redco-btn.btn-danger {
    background: var(--redco-danger);
    color: white;
    border-color: var(--redco-danger);
}

.redco-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* ===== ENHANCED FORM SYSTEM ===== */
.redco-form-group {
    margin-bottom: 24px;
}

.redco-form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--redco-text);
    font-size: 14px;
}

.redco-form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--redco-border);
    border-radius: var(--redco-radius);
    font-size: 14px;
    line-height: 1.4;
    color: var(--redco-text);
    background: var(--redco-bg-card);
    transition: var(--redco-transition);
}

.redco-form-control:focus {
    outline: none;
    border-color: var(--redco-primary);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.redco-form-help {
    margin-top: 6px;
    font-size: 13px;
    color: var(--redco-text-light);
    line-height: 1.4;
}

/* ===== ENHANCED STATISTICS SYSTEM ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 32px;
}

.stat-item {
    background: var(--redco-bg-card);
    border: 1px solid var(--redco-border);
    border-radius: var(--redco-radius);
    padding: 20px;
    text-align: center;
    transition: var(--redco-transition);
}

.stat-item:hover {
    box-shadow: var(--redco-shadow-md);
    border-color: #d1d5db;
}

.stat-value {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: var(--redco-primary);
    line-height: 1.2;
    margin-bottom: 4px;
}

.stat-label {
    display: block;
    font-size: 13px;
    color: var(--redco-text-light);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
    .redco-admin-container {
        grid-template-columns: 280px 1fr;
    }
}

@media (max-width: 768px) {
    .redco-admin-container {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 16px;
    }
    
    .card-header,
    .card-body,
    .card-footer {
        padding: 20px;
    }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

@media (prefers-contrast: high) {
    :root {
        --redco-border: #000000;
        --redco-text: #000000;
        --redco-text-light: #333333;
    }
}

/* ===== FOCUS MANAGEMENT ===== */
.redco-btn:focus,
.redco-form-control:focus {
    outline: 2px solid var(--redco-primary);
    outline-offset: 2px;
}

/* ===== LOADING STATES ===== */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--redco-border);
    border-top-color: var(--redco-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* ===== ENHANCED NAVIGATION SYSTEM ===== */
.redco-optimizer-tabs .nav-tab,
.nav-tab-wrapper .nav-tab {
    font-size: 15px !important;
    font-weight: 600 !important;
    line-height: 1.4 !important;
    transition: var(--redco-transition);
}

.redco-optimizer-tabs .nav-tab-active,
.nav-tab-wrapper .nav-tab-active {
    font-size: 15px !important;
    font-weight: 700 !important;
    line-height: 1.4 !important;
}

.redco-optimizer-tabs .nav-tab:not(.nav-tab-active) .dashicons,
.nav-tab-wrapper .nav-tab:not(.nav-tab-active) .dashicons {
    color: var(--redco-primary) !important;
}

.redco-nav .dashicons {
    margin-left: 16px;
    margin-right: 20px;
    width: 20px;
    height: 20px;
    font-size: 25px;
    opacity: 0.8;
    flex-shrink: 0;
    transition: var(--redco-transition);
    color: var(--redco-primary-dark);
}

.nav-title,
.redco-nav-title,
.navigation-title {
    font-weight: 600 !important;
    font-size: 14px !important;
    line-height: 1.8 !important;
    color: var(--redco-text) !important;
}

.nav-tab-active .nav-title,
.nav-tab-active .redco-nav-title,
.nav-tab-active .navigation-title,
.current .nav-title,
.current .redco-nav-title,
.current .navigation-title {
    color: #ffffff !important;
}

/* ===== ENHANCED HEADER SYSTEM ===== */
.module-header-section {
    background: linear-gradient(135deg, var(--redco-primary) 0%, var(--redco-primary-dark) 100%);
    margin: 0 -20px 30px -2px;
    padding: 0;
    color: white;
    border-radius: 0;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
    position: relative;
    overflow: hidden;
    min-height: 182px;
}

.module-header-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px 40px 20px 50px;
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 30px;
    align-items: start;
    position: relative;
    z-index: 1;
}

.header-main {
    display: flex;
    align-items: center;
    gap: 20px;
    min-width: 0;
}

.header-icon {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    flex-shrink: 0;
}

.header-icon .dashicons {
    font-size: 36px;
    width: 36px;
    height: 36px;
    color: white;
}

.header-text {
    flex: 1;
    min-width: 0;
}

.header-text h1 {
    color: white;
    margin: 0 0 6px 0;
    font-size: 28px;
    font-weight: 700;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-text p {
    color: rgba(255, 255, 255, 0.9);
    margin: 0 0 12px 0;
    font-size: 14px;
    line-height: 1.4;
    max-width: 500px;
}

/* ===== STATUS BADGES ===== */
.status-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    backdrop-filter: blur(10px);
}

.status-badge.enabled {
    background: rgba(76, 175, 80, 0.3);
    border-color: rgba(76, 175, 80, 0.5);
    color: #e8f5e8;
}

.status-badge.disabled {
    background: rgba(239, 68, 68, 0.3);
    border-color: rgba(239, 68, 68, 0.5);
    color: #fecaca;
}

.status-badge.performance {
    background: rgba(59, 130, 246, 0.3);
    border-color: rgba(59, 130, 246, 0.5);
    color: #dbeafe;
}

.status-badge .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* ===== ENHANCED LAYOUT COMPONENTS ===== */
.redco-module-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

.module-layout {
    display: flex;
    gap: 36px;
    align-items: stretch;
    min-height: calc(100vh - 300px);
}

.redco-content-main {
    flex: 1;
    min-width: 0;
}

.redco-content-sidebar {
    width: 280px;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
}

/* ===== ENHANCED SIDEBAR COMPONENTS ===== */
.sidebar-logo-section {
    width: 100%;
    margin-bottom: 12px;
    margin-top: 0;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.logo-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 6px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: var(--redco-transition);
}

.logo-container:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.sidebar-plugin-logo {
    width: 140px;
    height: 56px;
    border-radius: 0;
    background: none;
    padding: 0;
    box-shadow: none;
    transition: var(--redco-transition);
    border: none;
    display: block;
    cursor: pointer;
}

.sidebar-plugin-logo:hover {
    transform: scale(1.01);
}

/* ===== COMPACT STATS SYSTEM ===== */
.sidebar-compact-row {
    display: flex;
    gap: 6px;
    margin-bottom: 0;
    position: relative;
    z-index: 1;
    width: 100%;
    justify-content: center;
}

.compact-modules-stat {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.25);
    border-radius: 10px;
    padding: 8px 10px;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: var(--redco-transition);
    backdrop-filter: blur(10px);
    flex: 1;
    min-width: 0;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.compact-modules-stat:hover {
    background: rgba(255, 255, 255, 0.22);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.35);
}

.compact-stat-icon {
    width: 24px;
    height: 24px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.25);
    flex-shrink: 0;
    position: relative;
    z-index: 1;
}

.compact-stat-icon .dashicons {
    font-size: 12px;
    color: #fff;
    width: 12px;
    height: 12px;
}

.compact-stat-content {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    position: relative;
    z-index: 1;
}

.compact-stat-value {
    display: block;
    font-size: 12px;
    font-weight: 700;
    color: #fff;
    line-height: 1.1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.compact-stat-label {
    display: block;
    font-size: 8px;
    color: rgba(255, 255, 255, 0.85);
    font-weight: 500;
    margin-top: 1px;
    text-transform: uppercase;
    letter-spacing: 0.2px;
}

/* ===== ENHANCED RESPONSIVE DESIGN ===== */
@media (max-width: 1400px) {
    .redco-admin-container {
        max-width: 100%;
        padding: 0 10px;
    }

    .header-content {
        padding: 20px 30px;
        gap: 20px;
    }

    .header-icon {
        padding: 16px;
    }

    .header-icon .dashicons {
        font-size: 32px;
        width: 32px;
        height: 32px;
    }
}

@media (max-width: 1200px) {
    .redco-admin-container {
        grid-template-columns: 260px 1fr;
    }

    .redco-content-sidebar {
        width: 240px;
    }

    .module-layout {
        gap: 24px;
    }
}

@media (max-width: 992px) {
    .redco-admin-container {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
    }

    .redco-sidebar-header {
        border-radius: 0;
        min-height: 120px;
    }

    .sidebar-compact-row {
        flex-wrap: wrap;
        gap: 8px;
    }

    .compact-modules-stat {
        min-width: calc(50% - 4px);
    }

    .module-layout {
        flex-direction: column;
        gap: 20px;
    }

    .redco-content-sidebar {
        width: 100%;
        order: -1;
    }

    .header-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 16px;
    }

    .header-main {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .redco-module-tab {
        padding: 0 0 40px 0;
    }

    .redco-module-content {
        padding: 0 16px;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 12px;
    }

    .card-header,
    .card-body,
    .card-footer {
        padding: 16px;
    }

    .card-header h2,
    .card-header h3 {
        font-size: 16px;
    }

    .header-text h1 {
        font-size: 22px;
    }

    .header-icon {
        padding: 12px;
    }

    .header-icon .dashicons {
        font-size: 28px;
        width: 28px;
        height: 28px;
    }

    .redco-btn {
        padding: 8px 12px;
        font-size: 13px;
    }

    .compact-modules-stat {
        min-width: 100%;
        padding: 6px 8px;
    }

    .compact-stat-value {
        font-size: 11px;
    }

    .compact-stat-label {
        font-size: 7px;
    }
}

@media (max-width: 480px) {
    .redco-admin-container {
        padding: 0 5px;
    }

    .redco-module-content {
        padding: 0 12px;
    }

    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 8px;
    }

    .stat-item {
        padding: 12px;
    }

    .stat-value {
        font-size: 18px;
    }

    .stat-label {
        font-size: 11px;
    }

    .card-header,
    .card-body,
    .card-footer {
        padding: 12px;
    }

    .header-content {
        padding: 16px 20px;
    }

    .header-text h1 {
        font-size: 18px;
    }

    .header-text p {
        font-size: 13px;
    }

    .redco-form-control {
        padding: 8px 10px;
        font-size: 13px;
    }

    .redco-btn {
        padding: 6px 10px;
        font-size: 12px;
    }
}

/* ===== ENHANCED ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

@media (prefers-contrast: high) {
    :root {
        --redco-border: #000000;
        --redco-text: #000000;
        --redco-text-light: #333333;
        --redco-bg-card: #ffffff;
        --redco-bg-subtle: #f0f0f0;
    }

    .redco-card {
        border-width: 2px;
    }

    .redco-btn {
        border-width: 2px;
    }
}

@media (prefers-color-scheme: dark) {
    :root {
        --redco-bg-section: #1a1a1a;
        --redco-bg-card: #2a2a2a;
        --redco-bg-subtle: #1e1e1e;
        --redco-text: #e0e0e0;
        --redco-text-light: #b0b0b0;
        --redco-border: #404040;
        --redco-border-light: #353535;
    }

    .redco-module-tab {
        background: var(--redco-bg-subtle);
    }
}

/* ===== FOCUS MANAGEMENT ===== */
.redco-btn:focus,
.redco-form-control:focus,
.module-toggle:focus {
    outline: 2px solid var(--redco-primary);
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.2);
}

.redco-btn:focus:not(:focus-visible) {
    outline: none;
    box-shadow: none;
}

/* ===== LOADING STATES ===== */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--redco-border);
    border-top-color: var(--redco-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
}

.stat-updated {
    animation: highlight 1s ease-out;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes highlight {
    0% { background-color: rgba(76, 175, 80, 0.3); }
    100% { background-color: transparent; }
}

/* ===== NOTIFICATION SYSTEM ===== */
.redco-notification {
    position: fixed;
    top: 32px;
    right: 20px;
    background: var(--redco-bg-card);
    border: 1px solid var(--redco-border);
    border-radius: var(--redco-radius);
    padding: 12px 16px;
    box-shadow: var(--redco-shadow-lg);
    z-index: 9999;
    transform: translateX(100%);
    transition: var(--redco-transition);
    max-width: 300px;
}

.redco-notification.show {
    transform: translateX(0);
}

.redco-notification.success {
    border-left: 4px solid var(--redco-success);
}

.redco-notification.error {
    border-left: 4px solid var(--redco-danger);
}

.redco-notification.warning {
    border-left: 4px solid var(--redco-warning);
}

/* ===== SAVE INDICATORS ===== */
.save-indicator {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    margin-left: 8px;
    font-size: 12px;
    opacity: 0;
    transition: var(--redco-transition);
}

.save-indicator.saving {
    color: var(--redco-warning);
    opacity: 1;
}

.save-indicator.success {
    color: var(--redco-success);
    opacity: 1;
}

.save-indicator.error {
    color: var(--redco-danger);
    opacity: 1;
}

.save-indicator .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

.save-indicator.saving .dashicons {
    animation: spin 1s linear infinite;
}

/* ===== FORM VALIDATION ===== */
.redco-form-group.has-error .redco-form-control {
    border-color: var(--redco-danger);
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.invalid-feedback {
    display: block;
    margin-top: 4px;
    font-size: 12px;
    color: var(--redco-danger);
}

/* ===== SKIP LINKS ===== */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--redco-primary);
    color: white;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: var(--redco-radius);
    z-index: 10000;
    transition: var(--redco-transition);
}

.skip-link:focus {
    top: 6px;
}
